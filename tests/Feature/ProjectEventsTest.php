<?php

namespace Tests\Feature;

use App\Jobs\SendTaskReminders;
use App\Models\Candidate;
use App\Models\Invite;
use App\Models\Project;
use App\Models\Task;
use App\Models\TimeSlot;
use App\Models\User;
use App\Notifications\AssignedAsProjectManagerNotification;
use App\Notifications\InvitedToProject;
use App\Notifications\TaskReminders;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Tests\TenantAwareTestCase;

class ProjectEventsTest extends TenantAwareTestCase
{
    const string PROJECT_FORM_NAME = 'ProjectFormGrouped';
    const string LARAFORM_PROCESS_ENDPOINT = '/laraform/process';

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
        Mail::fake();
    }

    /**
     * @test
     */
    public function it_syncs_project_activity_subscriptions()
    {
        /** @var Project $project */
        $project = Project::factory()->create();
        $this->assertEquals(1, $project->activitySubscriptions()->count());

        /** @var User $userA */
        $userA = User::factory()->create();
        /** @var User $userB */
        $userB = User::factory()->create();

        $project->project_manager_id = $userA->id;
        $project->save();

        $this->assertEquals(1, $project->activitySubscriptions()->count());

        $project->users()->save($userB);

        $this->assertEquals(2, $project->activitySubscriptions()->count());

        $project->users()->detach($userB->id);

        $this->assertEquals(1, $project->activitySubscriptions()->count());
    }

    /**
     * @return void
     *
     * @test
     */
    public function it_doesnt_sent_day_agenda_if_nothing_to_send()
    {
        /** @var Project $project */
        $project = Project::factory()->create();

        /** @var User $user */
        $user = User::factory()->create();

        /** @var Candidate $candidate */
        $candidate = Candidate::factory()->create();

        $project->stages[0]->addCandidate($candidate);

        $task = Task::factory()->create([
            'project_id' => $project->id,
            'assignee_id' => $user->id,
            'deadline_at' => null,
            'done' => 0,
        ]);
        $candidate->tasks()->save($task);

        $candidate->anonymize();

        $reminders = new TaskReminders($user);

        $data = $reminders->getData();

        $this->assertEquals(0, $data['tasks_scheduled_today']->count());
        $this->assertEquals(0, $data['meetings_today']->count());
        $this->assertEquals(0, $data['incomplete_task_count']);
        $this->assertEquals(0, $data['overdue_task_count']);

        $this->assertFalse($reminders->shouldSend());

        (new SendTaskReminders)->handle();

        Notification::assertNothingSent();
    }

    /**
     * @test
     */
    public function it_sends_day_agenda()
    {
        Carbon::setTestNow(Carbon::parse('2023-01-04 12:00:00'));

        /** @var Task $task */
        $task = Task::factory()->create([
            'deadline_at' => now()->format('Y-m-d'),
        ])->refresh();

        $user = $task->assignee;

        /** @var TimeSlot $timeSlot */
        $timeSlot = TimeSlot::factory()->create();

        $invite = Invite::factory()->create([
            'event_set_id' => $timeSlot->event_set_id,
            'candidate_id' => Candidate::factory()->create()->id,
            'selected_time_slot_id' => $timeSlot->id,
        ]);

        $timeSlot->eventSet->users()->save($user);

        $reminders = new TaskReminders($user);

        $this->assertTrue($reminders->shouldSend());
        $data = $reminders->getData();
        $this->assertEquals(1, $data['tasks_scheduled_today']->count());
        $this->assertEquals(0, $data['overdue_task_count']);
        $this->assertEquals(1, $data['meetings_today']->count());

        $mail = $reminders->toMail($user);

        $this->assertCount(5, $mail->introLines);

        (new SendTaskReminders)->handle();

        Notification::assertTimesSent(1, TaskReminders::class);
    }

    /**
     * @test
     */
    public function limited_users_get_tasks_with_daily_agendas()
    {
        Carbon::setTestNow(Carbon::parse('2023-01-04 12:00:00'));

        $user = User::factory()->create(['role' => User::ROLE_LIMITED]);
        $project = Project::factory()->create();
        $project->users()->attach($user->id);

        /** @var Task $task */
        $task = Task::factory()->create([
            'deadline_at' => now()->format('Y-m-d'),
            'assignee_id' => $user->id,
            'project_id' => $project->id,
        ])->refresh();

        dispatch_sync(new SendTaskReminders);

        Notification::assertCount(1);
        Notification::assertSentTo($user, TaskReminders::class);
    }

    /**
     * @test
     */
    public function limited_users_get_overdue_tasks_with_daily_agendas()
    {
        Carbon::setTestNow(Carbon::parse('2023-01-04 12:00:00'));

        /** @var User $user */
        $user = User::factory()->create(['role' => User::ROLE_LIMITED]);

        /** @var Project $project */
        $project = Project::factory()->create();
        $project->users()->attach($user->id);

        /** @var Task $task */
        $task = Task::factory()->create([
            'deadline_at' => now()->subDays(3)->format('Y-m-d'),
            'assignee_id' => $user->id,
            'project_id' => $project->id,
        ])->refresh();

        $project->stages[0]->addCandidate($task->candidate);

        dispatch_sync(new SendTaskReminders);

        Notification::assertCount(1);
        Notification::assertSentTo($user, TaskReminders::class);
    }

    public function test_project_creation_does_not_notify_acting_user_as_project_manager()
    {
        $actingUser = User::factory()->create();

        $this->actingAs($actingUser)->post(static::LARAFORM_PROCESS_ENDPOINT, [
            'key' => encrypt(static::PROJECT_FORM_NAME),
            'data' => [
                'position_name' => 'My project',
                'accessible_only_members' => 0,
                'status' => Project::STATUS_IN_PROGRESS,
                'is_perpetual' => 0,
                'users' => [],
                'project_manager_id' => $actingUser->id,
                'is_template' => false,
            ],
        ])->assertOk();

        Notification::assertSentTimes(AssignedAsProjectManagerNotification::class, 0);
    }

    public function test_project_update_does_not_notify_acting_user_as_project_manager()
    {
        $actingUser = User::factory()->create();
        $project = Project::factory()->create();

        $key = encrypt(static::PROJECT_FORM_NAME);
        $this->actingAs($actingUser)->post(static::LARAFORM_PROCESS_ENDPOINT, [
            'key' => $key,
            'data' => [
                ...static::createNoopDataPayload($project),
                'project_manager_id' => $actingUser->id,
            ],
        ])->assertOk();

        Notification::assertSentTimes(AssignedAsProjectManagerNotification::class, 0);
    }

    public function test_project_creation_notifies_project_manager()
    {
        $actingUser = User::factory()->create();
        $manager = User::factory()->create();

        $this->actingAs($actingUser)->post(static::LARAFORM_PROCESS_ENDPOINT, [
            'key' => encrypt(static::PROJECT_FORM_NAME),
            'data' => [
                'position_name' => 'My project',
                'accessible_only_members' => 0,
                'status' => Project::STATUS_IN_PROGRESS,
                'is_perpetual' => 0,
                'users' => [],
                'project_manager_id' => $manager->id,
                'is_template' => false,
            ],
        ])->assertOk();

        Notification::assertSentTimes(AssignedAsProjectManagerNotification::class, 1);
        Notification::assertSentTo($manager, AssignedAsProjectManagerNotification::class);
    }

    public function test_project_update_notifies_project_manager()
    {
        $actingUser = User::factory()->create();
        $initialManager = User::factory()->create();
        $project = Project::factory()->create(['project_manager_id' => $initialManager->id]);
        $newManager = User::factory()->create();

        $key = encrypt(static::PROJECT_FORM_NAME);
        $this->actingAs($actingUser)->post(static::LARAFORM_PROCESS_ENDPOINT, [
            'key' => $key,
            'data' => [
                ...static::createNoopDataPayload($project),
                'project_manager_id' => $newManager->id,
            ],
        ])->assertOk();

        Notification::assertSentTimes(AssignedAsProjectManagerNotification::class, 1);
        Notification::assertSentTo($newManager, AssignedAsProjectManagerNotification::class);
        Notification::assertNotSentTo($actingUser, AssignedAsProjectManagerNotification::class);
    }

    private static function createNoopDataPayload(Project $project): array
    {
        return [
            'id' => $project->id,
            'position_name' => $project->position_name,
            'accessible_only_members' => intval($project->accessible_only_members),
            'status' => $project->status,
            'is_perpetual' => intval($project->is_perpetual),
            'users' => $project->users->pluck('id')->values()->all(),
            'project_manager_id' => $project->project_manager_id,
            'is_template' => false,
        ];
    }

    /**
     * @test
     */
    public function rec_2011_users_get_notified_only_once_if_project_edited()
    {
        $admin = User::factory()->create();
        $projectManager = User::factory()->create(['role' => User::ROLE_REGULAR]);
        $user = User::factory()->create(['role' => User::ROLE_LIMITED]);
        $user2 = User::factory()->create(['role' => User::ROLE_LIMITED]);

        Carbon::setTestNow(now()->addHours(2));

        $key = encrypt(static::PROJECT_FORM_NAME);
        $this->actingAs($admin)->post(static::LARAFORM_PROCESS_ENDPOINT, [
            'key' => $key,
            'data' => [
                'position_name' => 'My project',
                'accessible_only_members' => 0,
                'status' => Project::STATUS_IN_PROGRESS,
                'is_perpetual' => 0,
                'users' => [$user->id],
                'project_manager_id' => $admin->id,
                'is_template' => false,
            ],
        ])->assertOk();

        $this->actingAs($admin)->post(static::LARAFORM_PROCESS_ENDPOINT, [
            'key' => $key,
            'data' => [
                'id' => 1,
                'position_name' => 'My projectzz',
                'accessible_only_members' => 0,
                'status' => Project::STATUS_IN_PROGRESS,
                'is_perpetual' => 0,
                'users' => [$user->id, $user2->id],
                'project_manager_id' => $admin->id,
                'is_template' => false,
            ],
        ])->assertOk();

        $this->assertEquals(1, Project::query()->count());

        Notification::assertTimesSent(2, InvitedToProject::class);

    }
}
