<?php

namespace Tests\Feature;

use App\Models\Action;
use App\Models\Activity;
use App\Models\Candidate;
use App\Models\Message;
use App\Models\Project;
use App\Models\ProjectAction;
use App\Models\ProjectRole;
use App\Models\ProjectUser;
use App\Models\User;
use App\Notifications\PingUserAction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;
use Tests\FakeMailer;
use Tests\TenantAwareTestCase;

class ProjectRolesTest extends TenantAwareTestCase
{
    use FakeMailer;

    public function test_it_creates_project_roles()
    {
        $unrelatedUser = User::factory()->createOne(['role' => 'admin']);
        $user = User::factory()->createOne(['role' => 'admin']);
        $project = Project::factory()->createOne();
        $project->users()->save($user);

        // Cant create role for unrelated user
        $this->actingAs($user);
        $this->post('/project-roles/quick', [
            'name' => 'Test role',
            'user_id' => $unrelatedUser->id,
            'project_id' => $project->id,
        ])->assertStatus(404);

        // Can create role for related user
        $this->actingAs($user);
        $this->post('/project-roles/quick', [
            'name' => 'Test role',
            'user_id' => $user->id,
            'project_id' => $project->id,
        ])->assertStatus(200);

        $this->assertDatabaseHas('project_roles', [
            'name' => 'Test role',
        ]);

        $this->assertDatabaseHas('project_user_roles', [
            'project_user_id' => $project->users()->where('user_id', $user->id)->first()->pivot->id,
            'project_role_id' => $project->id,
        ]);
    }

    public function test_it_can_query_all_roles()
    {
        $projectRoles = ProjectRole::factory()->count(10)->create();

        $user = User::factory()->createOne(['role' => 'admin']);
        $project = Project::factory()->createOne();

        $this->actingAs($user);
        $this->get('/project-roles/all')->assertStatus(200)->assertJsonCount(10);
    }

    public function test_it_can_create_single_role_in_admin()
    {
        $user = User::factory()->createOne(['role' => 'admin']);
        $this->actingAs($user);

        // Post ProjectRoleForm
        $this->post('/laraform/process', [
            'key' => encrypt('ProjectRoleForm'),
            'data' => [
                'name' => 'Test role',
                'color' => '#d6f5dd',
            ],
        ])->assertStatus(200);

        $this->assertDatabaseHas('project_roles', [
            'name' => 'Test role',
        ], 'tenant');
    }

    public function test_it_can_assign_role_to_user()
    {
        $user = User::factory()->createOne(['role' => 'admin']);
        $project = Project::factory()->createOne();
        $role = ProjectRole::factory()->createOne();
        $project->users()->save($user);

        $this->actingAs($user);

        $this->putJson('/project-roles/sync', [
            'user_id' => $user->id,
            'project_id' => $project->id,
            'role_ids' => [$role->id],
        ])->assertStatus(204);

        $this->assertDatabaseHas('project_user_roles', [
            'project_user_id' => $project->users()->first()->id,
            'project_role_id' => $role->id,
        ]);
    }

    public function test_project_role_trigger_works()
    {
        $project = Project::factory()->createOne();
        $user1 = $project->users()->first();

        $this->assertDatabaseHas('project_user', [
            'user_id' => $user1->id,
            'project_id' => $project->id,
            'is_manager' => true,
        ], 'tenant');

        $newUser = User::factory()->createOne();
        $this->assertEquals(1, ProjectUser::count());

        $project->project_manager_id = $newUser->id;
        $project->saveQuietly();

        $this->assertEquals(2, ProjectUser::count());

        $this->assertDatabaseHas('project_user', [
            'user_id' => $user1->id,
            'project_id' => $project->id,
            'is_manager' => false,
        ], 'tenant');

        $this->assertDatabaseHas('project_user', [
            'user_id' => $newUser->id,
            'project_id' => $project->id,
            'is_manager' => true,
        ], 'tenant');
    }

    public function test_project_role_trigger_works_when_creating_new_project()
    {
        // Create new project using API
        $user = User::factory()->createOne(['role' => 'admin']);
        $this->actingAs($user);

        $userTwo = User::factory()->createOne();

        $project = $this->post('/laraform/process', [
            'key' => encrypt('ProjectFormGrouped'),
            'data' => [
                'position_name' => 'Testing project',
                'status' => Project::STATUS_DRAFT,
                'project_manager_id' => $user->id,
                'accessible_only_members' => 0,
                'is_perpetual' => 0,
                'users' => [$userTwo->id],
                'is_template' => false,
            ],
        ])->json();

        $this->assertDatabaseHas('project_user', [
            'user_id' => $user->id,
            'project_id' => $project['payload']['updates']['id'],
            'is_manager' => true,
        ], 'tenant');

        $this->assertDatabaseHas('project_user', [
            'user_id' => $userTwo->id,
            'project_id' => $project['payload']['updates']['id'],
            'is_manager' => false,
        ], 'tenant');
    }

    public function test_project_action_role_ids_filter_works()
    {
        // 1. Create a new project with four users
        $project = Project::factory()->create(['status' => Project::STATUS_IN_PROGRESS]);
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create();
        $user4 = User::factory()->create();

        // Add all users to the project
        $project->users()->attach($user1);
        $project->users()->attach($user2);
        $project->users()->attach($user3);
        $project->users()->attach($user4);

        // 2. Create a new ProjectRole and attach it to two users
        $role = ProjectRole::factory()->create(['name' => 'Test Role']);

        // Attach the role to user1 and user2 only
        $project->users()->where('user_id', $user1->id)->first()->pivot->roles()->sync($role->id);
        $project->users()->where('user_id', $user2->id)->first()->pivot->roles()->sync($role->id);

        // Create an activity to trigger the action
        Activity::log(Activity::PROJECT_STATUS_CHANGED, [
            $project,
        ], null, null, ['from' => Project::STATUS_DRAFT, 'to' => Project::STATUS_IN_PROGRESS]);

        // 3. Create a new ProjectAction with role_ids filter
        $action = ProjectAction::create([
            'action_type' => ProjectAction::TYPE_USER_MESSAGE,
            'project_id' => $project->id,
            'name' => 'Test project action with role filter',
            'trigger' => ProjectAction::TRIGGER_PROJECT_STATUS_CHANGE_AUTOMATIC,
            'data' => [
                'subject' => 'Test Subject',
                'body' => 'Test Body',
                'send_to_type' => Action::SEND_TO_TYPE_SELECT_ROLES,
                'role_ids' => [$role->id],
            ],
            'delay' => ['unit' => 'minutes', 'value' => '5'],
        ]);

        // Setup fake mailer to capture emails
        $fakeMailer = $this->getFakeMailer();
        $this->swapMailer($fakeMailer);

        // 4. Trigger the action (simulate time passing)
        Carbon::setTestNow(now()->addMinutes(6));
        $action->sendAutomatic();

        // 5. Assert that only the two members with the role received emails
        $this->assertEquals(1, $action->done()->count());

        // Assert that the message was created
        $message = Message::where('subject', 'Test Subject')->where('body', 'Test Body')->first();
        $this->assertNotNull($message);

        // Assert that only two users received the message
        $this->assertEquals(2, $message->users()->count());

        // Assert that user1 and user2 received the message
        $this->assertDatabaseHas('messageables', [
            'message_id' => $message->id,
            'messageable_id' => $user1->id,
            'messageable_type' => User::class,
        ], 'tenant');

        $this->assertDatabaseHas('messageables', [
            'message_id' => $message->id,
            'messageable_id' => $user2->id,
            'messageable_type' => User::class,
        ], 'tenant');

        // Assert that user3 and user4 did NOT receive the message
        $this->assertDatabaseMissing('messageables', [
            'message_id' => $message->id,
            'messageable_id' => $user3->id,
            'messageable_type' => User::class,
        ], 'tenant');

        $this->assertDatabaseMissing('messageables', [
            'message_id' => $message->id,
            'messageable_id' => $user4->id,
            'messageable_type' => User::class,
        ], 'tenant');
    }

    public function test_stage_action_role_ids_filter_works()
    {
        // 1. Create a new project with four users
        $project = Project::factory()->create(['status' => Project::STATUS_IN_PROGRESS]);
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create();
        $user4 = User::factory()->create();

        // Add all users to the project
        $project->users()->attach($user1);
        $project->users()->attach($user2);
        $project->users()->attach($user3);
        $project->users()->attach($user4);

        // 2. Create a new ProjectRole and attach it to two users
        $role = ProjectRole::factory()->create(['name' => 'Test Role']);

        // Attach the role to user1 and user2 only
        $project->users()->where('user_id', $user1->id)->first()->pivot->roles()->sync($role->id);
        $project->users()->where('user_id', $user2->id)->first()->pivot->roles()->sync($role->id);

        // Create a stage to add the action to
        $stage = $project->stages()->first();

        // Create a candidate to trigger the action
        $candidate = Candidate::factory()->create();
        $stage->addCandidate($candidate);

        // 3. Create a new Stage Action (Action.php) with role_ids filter
        $action = Action::create([
            'action_type' => Action::TYPE_PING_USER,
            'stage_id' => $stage->id,
            'name' => 'Test stage action with role filter',
            'trigger' => Action::TRIGGER_MANUAL,
            'data' => [
                'subject' => 'Test Subject',
                'call_to_action' => 'Test Body',
                'send_to_type' => Action::SEND_TO_TYPE_SELECT_ROLES,
                'role_ids' => [$role->id],
            ],
        ]);

        // Setup fake mailer to capture notifications
        Notification::fake();

        // 4. Trigger the action
        $action->execute();

        // 5. Assert that only the two members with the role received notifications
        Notification::assertSentTo(
            [$user1, $user2],
            PingUserAction::class
        );

        Notification::assertNotSentTo(
            [$user3, $user4],
            PingUserAction::class
        );
    }

    public function test_project_role_trigger_works_when_creating_new_project_with_grouped_form()
    {
        // Create two users
        $user = User::factory()->createOne(['role' => 'admin']);
        $this->actingAs($user);

        $userTwo = User::factory()->createOne();

        // Post ProjectFormGrouped to /laraform/process
        $project = $this->post('/laraform/process', [
            'key' => encrypt('ProjectFormGrouped'),
            'data' => [
                'position_name' => 'Testing grouped project',
                'status' => Project::STATUS_DRAFT,
                'project_manager_id' => $user->id,
                'accessible_only_members' => 0,
                'is_perpetual' => 0,
                'is_template' => false,
                'users' => [$userTwo->id],
            ],
        ])->json();

        // Assert that the created project has one admin user (project manager)
        $this->assertDatabaseHas('project_user', [
            'user_id' => $user->id,
            'project_id' => $project['payload']['updates']['id'],
            'is_manager' => true,
        ], 'tenant');

        // Assert that the created project has one regular user
        $this->assertDatabaseHas('project_user', [
            'user_id' => $userTwo->id,
            'project_id' => $project['payload']['updates']['id'],
            'is_manager' => false,
        ], 'tenant');
    }
}
