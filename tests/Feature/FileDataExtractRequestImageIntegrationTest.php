<?php

namespace Tests\Feature;

use App\Models\File;
use App\Services\AI\FileDataExtractRequest;
use Tests\TenantAwareTestCase;

class FileDataExtractRequestImageIntegrationTest extends TenantAwareTestCase
{
    public function test_file_data_extract_request_handles_image_files()
    {
        // Create a test image file
        $testImagePath = sys_get_temp_dir() . '/test_integration_image.png';
        $image = imagecreate(100, 50);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        imagefill($image, 0, 0, $white);
        imagestring($image, 5, 10, 15, 'Test Document', $black);
        imagepng($image, $testImagePath);
        imagedestroy($image);

        try {
            // Create a File model with image location
            $file = new File([
                'location' => 'test/path/document.png',
                'type' => File::TYPE_CANDIDATE_OTHER,
                'contents' => null,
            ]);

            // Mock the getLocalPath method to return our test image
            $file = $this->getMockBuilder(File::class)
                ->onlyMethods(['getLocalPath'])
                ->setConstructorArgs([[
                    'location' => 'test/path/document.png',
                    'type' => File::TYPE_CANDIDATE_OTHER,
                    'contents' => null,
                ]])
                ->getMock();

            $file->method('getLocalPath')->willReturn($testImagePath);

            // Test that the file is recognized as an image
            $this->assertTrue($file->isImage());

            // Create the AI request
            $request = new FileDataExtractRequest($file);

            // Test that filePath returns the local path
            $this->assertEquals($testImagePath, $request->filePath());

            // Test that getUserPrompt returns image-specific prompt
            $userPrompt = $request->getUserPrompt();
            $this->assertStringContainsString('Extract data from the document shown in the image', $userPrompt);

            // Test that getSystemPrompt mentions image handling
            $systemPrompt = $request->getSystemPrompt();
            $this->assertStringContainsString('text contents of a file or an image of the document', $systemPrompt);

        } finally {
            // Clean up
            if (file_exists($testImagePath)) {
                unlink($testImagePath);
            }
        }
    }

    public function test_file_data_extract_request_handles_non_image_files()
    {
        // Create a File model with non-image location
        $file = new File([
            'location' => 'test/path/document.pdf',
            'type' => File::TYPE_CV,
            'contents' => 'This is a test PDF content with some text.',
        ]);

        // Test that the file is not recognized as an image
        $this->assertFalse($file->isImage());

        // Create the AI request
        $request = new FileDataExtractRequest($file);

        // Test that filePath returns null
        $this->assertNull($request->filePath());

        // Test that getUserPrompt returns text-specific prompt
        $userPrompt = $request->getUserPrompt();
        $this->assertStringContainsString('Extract data from the following file', $userPrompt);
        $this->assertStringContainsString('This is a test PDF content', $userPrompt);
    }
}
