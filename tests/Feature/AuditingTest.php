<?php

namespace Tests\Feature;

use App\Models\Project;
use App\Models\ProjectUser;
use App\Models\User;
use OwenIt\Auditing\Models\Audit;
use Tests\TenantAwareTestCase;

class AuditingTest extends TenantAwareTestCase
{
    /**
     * @test
     */
    public function it_creates_audit_logs_for_many_to_many()
    {
        $user = User::factory()->create();
        $user2 = User::factory()->create();

        $res = $this->actingAs($user)->post('/laraform/process', [
            'key' => encrypt('ProjectFormGrouped'),
            'data' => [
                'position_name' => 'Testing project',
                'status' => Project::STATUS_DRAFT,
                'project_manager_id' => $user->id,
                'accessible_only_members' => 0,
                'is_perpetual' => 0,
                'users' => [$user2->id],
                'is_template' => 'false',
            ],
        ]);

        $this->assertEquals(1, Project::query()->count());
        $this->assertEquals(2, Audit::query()->where('auditable_type', ProjectUser::class)->count());
    }

    protected function setUp(): void
    {
        parent::setUp();
        // all tests are run with auditing enabled
        // @see TenantAwareTestCase
        //        $website = Helpers::getCurrentWebsite();
        //        $features = $website->features;
        //        $features[Website::FEATURE_AUDIT_LOG] = true;
        //        $website->features = $features;
        //        $website->save();
        //        config(['audit.enabled' => true]);
    }

    protected function tearDown(): void
    {
        config(['audit.enabled' => false]);
        //        parent::tearDown();
    }

}
