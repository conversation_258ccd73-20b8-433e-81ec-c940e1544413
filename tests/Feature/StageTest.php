<?php

namespace Tests\Feature;

use App\Models\Action;
use App\Models\Project;
use App\Models\Stage;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use OwenIt\Auditing\Models\Audit;
use Tests\TenantAwareTestCase;

class StageTest extends TenantAwareTestCase
{
    use RefreshDatabase;

    private Project $templateProject;
    private Project $childProject1;
    private Project $childProject2;
    private Stage $templateStage;
    private Stage $childStage1;
    private Stage $childStage2;
    private Action $templateAction1;
    private Action $templateAction2;
    private Action $childAction1;
    private Action $childAction2;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->admin()->create();
        $this->actingAs($this->user);
        
        // Create template project with stages and actions
        $this->templateProject = Project::factory()->create([
            'is_template' => true,
            'position_name' => 'Template Project',
        ]);
        
        // Create a template stage
        $this->templateStage = Stage::factory()->create([
            'project_id' => $this->templateProject->id,
            'name' => 'Template Stage',
            'is_template' => true,
        ]);
        
        // Create template actions
        $this->templateAction1 = Action::factory()->create([
            'stage_id' => $this->templateStage->id,
            'name' => 'Template Action 1',
            'action_type' => Action::TYPE_MOVE_TO_STAGE,
            'is_template' => true,
        ]);
        
        $this->templateAction2 = Action::factory()->create([
            'stage_id' => $this->templateStage->id,
            'name' => 'Template Action 2',
            'action_type' => Action::TYPE_USER_MESSAGE,
            'is_template' => true,
        ]);
        
        // Create child projects
        $this->childProject1 = Project::factory()->create([
            'is_template' => false,
            'position_name' => 'Child Project 1',
        ]);
        
        $this->childProject2 = Project::factory()->create([
            'is_template' => false,
            'position_name' => 'Child Project 2',
        ]);
        
        // Create child stages that reference the template
        $this->childStage1 = Stage::factory()->create([
            'project_id' => $this->childProject1->id,
            'name' => 'Child Stage 1',
            'template_id' => $this->templateStage->id,
        ]);
        
        $this->childStage2 = Stage::factory()->create([
            'project_id' => $this->childProject2->id,
            'name' => 'Child Stage 2',
            'template_id' => $this->templateStage->id,
        ]);
        
        // Create child actions that reference the template actions
        $this->childAction1 = Action::factory()->create([
            'stage_id' => $this->childStage1->id,
            'name' => 'Child Action 1',
            'template_id' => $this->templateAction1->id,
        ]);
        
        $this->childAction2 = Action::factory()->create([
            'stage_id' => $this->childStage2->id,
            'name' => 'Child Action 2',
            'template_id' => $this->templateAction2->id,
        ]);
    }

    /** @test */
    public function it_can_delete_template_stage_with_related_child_stages()
    {
        // Verify initial state
        $this->assertDatabaseHas('stages', ['id' => $this->templateStage->id]);
        $this->assertDatabaseHas('stages', ['id' => $this->childStage1->id, 'template_id' => $this->templateStage->id]);
        $this->assertDatabaseHas('stages', ['id' => $this->childStage2->id, 'template_id' => $this->templateStage->id]);
        
        // Delete the template stage
        $this->templateStage->delete();
        
        // Verify template stage is deleted
        $this->assertDatabaseMissing('stages', ['id' => $this->templateStage->id]);
        
        // Verify child stages still exist but template_id is set to null
        $this->assertDatabaseHas('stages', ['id' => $this->childStage1->id, 'template_id' => null]);
        $this->assertDatabaseHas('stages', ['id' => $this->childStage2->id, 'template_id' => null]);
    }

    /** @test */
    public function it_can_delete_template_stage_with_related_child_actions()
    {
        // Verify initial state
        $this->assertDatabaseHas('actions', ['id' => $this->templateAction1->id]);
        $this->assertDatabaseHas('actions', ['id' => $this->templateAction2->id]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction1->id, 'template_id' => $this->templateAction1->id]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction2->id, 'template_id' => $this->templateAction2->id]);
        
        // Delete the template stage (which should also handle related actions)
        $this->templateStage->delete();
        
        // Verify template stage and actions are deleted
        $this->assertDatabaseMissing('stages', ['id' => $this->templateStage->id]);
        $this->assertDatabaseMissing('actions', ['id' => $this->templateAction1->id]);
        $this->assertDatabaseMissing('actions', ['id' => $this->templateAction2->id]);
        
        // Verify child actions still exist but template_id is set to null
        $this->assertDatabaseHas('actions', ['id' => $this->childAction1->id, 'template_id' => null]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction2->id, 'template_id' => null]);
    }

    /** @test */
    public function it_creates_audit_logs_when_deleting_template_stage_with_related_models()
    {
        // Clear any existing audit logs
        Audit::query()->delete();
        
        // Delete the template stage
        $this->templateStage->delete();
        
        // Verify audit logs were created for the stage deletion
        $this->assertDatabaseHas('audits', [
            'auditable_type' => Stage::class,
            'auditable_id' => $this->templateStage->id,
            'event' => 'deleted',
        ]);
        
        // Verify audit logs were created for child stage updates (template_id set to null)
        $this->assertDatabaseHas('audits', [
            'auditable_type' => Stage::class,
            'auditable_id' => $this->childStage1->id,
            'event' => 'updated',
        ]);
        
        $this->assertDatabaseHas('audits', [
            'auditable_type' => Stage::class,
            'auditable_id' => $this->childStage2->id,
            'event' => 'updated',
        ]);
        
        // Verify audit logs were created for child action updates (template_id set to null)
        $this->assertDatabaseHas('audits', [
            'auditable_type' => Action::class,
            'auditable_id' => $this->childAction1->id,
            'event' => 'updated',
        ]);
        
        $this->assertDatabaseHas('audits', [
            'auditable_type' => Action::class,
            'auditable_id' => $this->childAction2->id,
            'event' => 'updated',
        ]);
        
        // Verify the audit logs contain the correct old and new values for template_id
        $childStage1Audit = Audit::where('auditable_type', Stage::class)
            ->where('auditable_id', $this->childStage1->id)
            ->where('event', 'updated')
            ->first();
        
        $this->assertNotNull($childStage1Audit);
        $this->assertEquals($this->templateStage->id, $childStage1Audit->old_values['template_id']);
        $this->assertNull($childStage1Audit->new_values['template_id']);
        
        $childAction1Audit = Audit::where('auditable_type', Action::class)
            ->where('auditable_id', $this->childAction1->id)
            ->where('event', 'updated')
            ->first();
        
        $this->assertNotNull($childAction1Audit);
        $this->assertEquals($this->templateAction1->id, $childAction1Audit->old_values['template_id']);
        $this->assertNull($childAction1Audit->new_values['template_id']);
    }

    /** @test */
    public function it_does_not_affect_child_models_when_deleting_non_template_stage()
    {
        // Create a non-template project and stage
        $regularProject = Project::factory()->create(['is_template' => false]);
        $regularStage = Stage::factory()->create([
            'project_id' => $regularProject->id,
            'name' => 'Regular Stage',
        ]);
        
        // Create an action for the regular stage
        $regularAction = Action::factory()->create([
            'stage_id' => $regularStage->id,
            'name' => 'Regular Action',
        ]);
        
        // Verify initial state - child stages should still reference template
        $this->assertDatabaseHas('stages', ['id' => $this->childStage1->id, 'template_id' => $this->templateStage->id]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction1->id, 'template_id' => $this->templateAction1->id]);
        
        // Delete the regular (non-template) stage
        $regularStage->delete();
        
        // Verify regular stage and action are deleted
        $this->assertDatabaseMissing('stages', ['id' => $regularStage->id]);
        $this->assertDatabaseMissing('actions', ['id' => $regularAction->id]);
        
        // Verify child stages and actions are unaffected (still reference template)
        $this->assertDatabaseHas('stages', ['id' => $this->childStage1->id, 'template_id' => $this->templateStage->id]);
        $this->assertDatabaseHas('stages', ['id' => $this->childStage2->id, 'template_id' => $this->templateStage->id]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction1->id, 'template_id' => $this->templateAction1->id]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction2->id, 'template_id' => $this->templateAction2->id]);
    }

    /** @test */
    public function it_handles_deletion_when_no_child_models_exist()
    {
        // Create a template stage with no children
        $isolatedTemplateProject = Project::factory()->create(['is_template' => true]);
        $isolatedTemplateStage = Stage::factory()->create([
            'project_id' => $isolatedTemplateProject->id,
            'name' => 'Isolated Template Stage',
            'is_template' => true,
        ]);
        
        $isolatedTemplateAction = Action::factory()->create([
            'stage_id' => $isolatedTemplateStage->id,
            'name' => 'Isolated Template Action',
            'is_template' => true,
        ]);
        
        // Delete the isolated template stage
        $isolatedTemplateStage->delete();
        
        // Verify deletion was successful
        $this->assertDatabaseMissing('stages', ['id' => $isolatedTemplateStage->id]);
        $this->assertDatabaseMissing('actions', ['id' => $isolatedTemplateAction->id]);
        
        // Verify other template relationships are unaffected
        $this->assertDatabaseHas('stages', ['id' => $this->childStage1->id, 'template_id' => $this->templateStage->id]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction1->id, 'template_id' => $this->templateAction1->id]);
    }

    /** @test */
    public function it_handles_complex_template_hierarchy()
    {
        // Create additional layers of template relationships
        $grandchildProject = Project::factory()->create(['is_template' => false]);
        $grandchildStage = Stage::factory()->create([
            'project_id' => $grandchildProject->id,
            'name' => 'Grandchild Stage',
            'template_id' => $this->childStage1->id, // References child, not template
        ]);
        
        $grandchildAction = Action::factory()->create([
            'stage_id' => $grandchildStage->id,
            'name' => 'Grandchild Action',
            'template_id' => $this->childAction1->id, // References child, not template
        ]);
        
        // Delete the template stage
        $this->templateStage->delete();
        
        // Verify template stage is deleted
        $this->assertDatabaseMissing('stages', ['id' => $this->templateStage->id]);
        
        // Verify direct children have template_id set to null
        $this->assertDatabaseHas('stages', ['id' => $this->childStage1->id, 'template_id' => null]);
        $this->assertDatabaseHas('stages', ['id' => $this->childStage2->id, 'template_id' => null]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction1->id, 'template_id' => null]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction2->id, 'template_id' => null]);
        
        // Verify grandchildren still reference their immediate parents (not affected by template deletion)
        $this->assertDatabaseHas('stages', ['id' => $grandchildStage->id, 'template_id' => $this->childStage1->id]);
        $this->assertDatabaseHas('actions', ['id' => $grandchildAction->id, 'template_id' => $this->childAction1->id]);
    }

    /** @test */
    public function it_preserves_audit_trail_user_information()
    {
        // Clear any existing audit logs
        Audit::query()->delete();
        
        // Delete the template stage as the authenticated user
        $this->templateStage->delete();
        
        // Verify audit logs contain correct user information
        $stageAudit = Audit::where('auditable_type', Stage::class)
            ->where('auditable_id', $this->childStage1->id)
            ->where('event', 'updated')
            ->first();
        
        $this->assertNotNull($stageAudit);
        $this->assertEquals($this->user->id, $stageAudit->user_id);
        $this->assertEquals(User::class, $stageAudit->user_type);
        
        $actionAudit = Audit::where('auditable_type', Action::class)
            ->where('auditable_id', $this->childAction1->id)
            ->where('event', 'updated')
            ->first();
        
        $this->assertNotNull($actionAudit);
        $this->assertEquals($this->user->id, $actionAudit->user_id);
        $this->assertEquals(User::class, $actionAudit->user_type);
    }

    /** @test */
    public function it_handles_multiple_actions_per_stage_correctly()
    {
        // Create additional actions for the template stage
        $additionalTemplateAction1 = Action::factory()->create([
            'stage_id' => $this->templateStage->id,
            'name' => 'Additional Template Action 1',
            'is_template' => true,
        ]);
        
        $additionalTemplateAction2 = Action::factory()->create([
            'stage_id' => $this->templateStage->id,
            'name' => 'Additional Template Action 2',
            'is_template' => true,
        ]);
        
        // Create child actions that reference these additional template actions
        $additionalChildAction1 = Action::factory()->create([
            'stage_id' => $this->childStage1->id,
            'name' => 'Additional Child Action 1',
            'template_id' => $additionalTemplateAction1->id,
        ]);
        
        $additionalChildAction2 = Action::factory()->create([
            'stage_id' => $this->childStage2->id,
            'name' => 'Additional Child Action 2',
            'template_id' => $additionalTemplateAction2->id,
        ]);
        
        // Delete the template stage
        $this->templateStage->delete();
        
        // Verify all template actions are deleted
        $this->assertDatabaseMissing('actions', ['id' => $this->templateAction1->id]);
        $this->assertDatabaseMissing('actions', ['id' => $this->templateAction2->id]);
        $this->assertDatabaseMissing('actions', ['id' => $additionalTemplateAction1->id]);
        $this->assertDatabaseMissing('actions', ['id' => $additionalTemplateAction2->id]);
        
        // Verify all child actions have template_id set to null
        $this->assertDatabaseHas('actions', ['id' => $this->childAction1->id, 'template_id' => null]);
        $this->assertDatabaseHas('actions', ['id' => $this->childAction2->id, 'template_id' => null]);
        $this->assertDatabaseHas('actions', ['id' => $additionalChildAction1->id, 'template_id' => null]);
        $this->assertDatabaseHas('actions', ['id' => $additionalChildAction2->id, 'template_id' => null]);
    }
}
