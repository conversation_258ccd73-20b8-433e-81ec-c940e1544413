<?php

namespace Tests\Unit;

use App\Models\File;
use App\Services\AI\FileDataExtractRequest;
use Mockery;
use Tests\TestCase;

class FileDataExtractRequestImageTest extends TestCase
{
    public function test_file_path_returns_null_for_non_image_files()
    {
        $file = Mockery::mock(File::class);
        $file->shouldReceive('isImage')->andReturn(false);
        $file->shouldReceive('getAttribute')->with('has_contents')->andReturn(true);
        $file->shouldReceive('getAttribute')->with('contents')->andReturn('Some file contents');

        $request = new FileDataExtractRequest($file);

        $this->assertNull($request->filePath());
    }

    public function test_file_path_returns_local_path_for_image_files()
    {
        $file = Mockery::mock(File::class);
        $file->shouldReceive('isImage')->andReturn(true);
        $file->shouldReceive('getLocalPath')->andReturn('/path/to/image.jpg');
        $file->shouldReceive('getAttribute')->with('has_contents')->andReturn(true);
        $file->shouldReceive('getAttribute')->with('contents')->andReturn('Some file contents');

        $request = new FileDataExtractRequest($file);

        $this->assertEquals('/path/to/image.jpg', $request->filePath());
    }

    public function test_file_path_returns_null_when_get_local_path_throws_exception()
    {
        $file = Mockery::mock(File::class);
        $file->shouldReceive('isImage')->andReturn(true);
        $file->shouldReceive('getLocalPath')->andThrow(new \Exception('Cannot get local path'));
        $file->shouldReceive('getAttribute')->with('has_contents')->andReturn(true);
        $file->shouldReceive('getAttribute')->with('contents')->andReturn('Some file contents');

        $request = new FileDataExtractRequest($file);

        $this->assertNull($request->filePath());
    }

    public function test_get_user_prompt_returns_image_specific_prompt_for_images()
    {
        $file = Mockery::mock(File::class);
        $file->shouldReceive('isImage')->andReturn(true);
        $file->shouldReceive('getAttribute')->with('has_contents')->andReturn(true);
        $file->shouldReceive('getAttribute')->with('contents')->andReturn('Some file contents');

        $request = new FileDataExtractRequest($file);

        $prompt = $request->getUserPrompt();
        $this->assertStringContainsString('Extract data from the document shown in the image', $prompt);
        $this->assertStringContainsString('dates, document types, and any validity periods', $prompt);
    }

    public function test_get_user_prompt_returns_text_prompt_for_non_images()
    {
        $file = Mockery::mock(File::class);
        $file->shouldReceive('isImage')->andReturn(false);
        $file->shouldReceive('getAttribute')->with('has_contents')->andReturn(true);
        $file->shouldReceive('getAttribute')->with('contents')->andReturn('Some file contents');

        $request = new FileDataExtractRequest($file);

        $prompt = $request->getUserPrompt();
        $this->assertStringContainsString('Extract data from the following file', $prompt);
        $this->assertStringContainsString('Some file contents', $prompt);
    }

    // Note: System prompt test removed due to database dependency complexity in unit tests
    // The system prompt functionality is tested in integration tests

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
