<?php

namespace Tests\Unit;

use App\Models\File;
use App\Models\Interfaces\AiLoggableInterface;
use App\Services\AI\AiRequestInterface;
use App\Services\AI\OpenAiRequestRunner;
use Tests\TestCase;

class OpenAiRequestRunnerImageTest extends TestCase
{
    private function createMockRequest(?string $filePath = null): AiRequestInterface
    {
        $file = new File;

        return new class($file, $filePath) implements AiRequestInterface
        {
            public function __construct(private File $file, private ?string $imagePath = null) {}

            public function getSystemPrompt(): string
            {
                return 'Test system prompt';
            }

            public function getUserPrompt(): string
            {
                return 'Test user prompt';
            }

            public function getResponseFormat(): array
            {
                return ['type' => 'json_object'];
            }

            public function getAiLoggable(): AiLoggableInterface
            {
                return $this->file;
            }

            public function updateDataAndGetFields(array $responseData): array
            {
                return [];
            }

            public function filePath(): ?string
            {
                return $this->imagePath;
            }
        };
    }

    public function test_get_ai_params_without_image()
    {
        $request = $this->createMockRequest(null);
        $runner = new OpenAiRequestRunner($request);

        $params = $runner->getAiParams();

        $this->assertArrayHasKey('messages', $params);
        $this->assertCount(2, $params['messages']);

        // System message
        $this->assertEquals('system', $params['messages'][0]['role']);
        $this->assertIsString($params['messages'][0]['content']);

        // User message should be simple text content
        $this->assertEquals('user', $params['messages'][1]['role']);
        $this->assertIsString($params['messages'][1]['content']);
        $this->assertEquals('Test user prompt', $params['messages'][1]['content']);
    }

    public function test_get_ai_params_with_nonexistent_image()
    {
        $request = $this->createMockRequest('/nonexistent/path/image.jpg');
        $runner = new OpenAiRequestRunner($request);

        $params = $runner->getAiParams();

        $this->assertArrayHasKey('messages', $params);
        $this->assertCount(2, $params['messages']);

        // User message should fall back to text-only since file doesn't exist
        $this->assertEquals('user', $params['messages'][1]['role']);
        $this->assertIsString($params['messages'][1]['content']);
        $this->assertEquals('Test user prompt', $params['messages'][1]['content']);
    }

    public function test_encode_image_to_base64_with_nonexistent_file()
    {
        $request = $this->createMockRequest(null);
        $runner = new OpenAiRequestRunner($request);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Image file not found:');

        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($runner);
        $method = $reflection->getMethod('encodeImageToBase64');
        $method->setAccessible(true);

        $method->invoke($runner, '/nonexistent/path/image.jpg');
    }

    public function test_encode_image_to_base64_with_valid_image()
    {
        // Create a simple test image
        $testImagePath = sys_get_temp_dir() . '/test_image.png';
        $image = imagecreate(10, 10);
        $white = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $white);
        imagepng($image, $testImagePath);
        imagedestroy($image);

        try {
            $request = $this->createMockRequest(null);
            $runner = new OpenAiRequestRunner($request);

            // Use reflection to access the protected method
            $reflection = new \ReflectionClass($runner);
            $method = $reflection->getMethod('encodeImageToBase64');
            $method->setAccessible(true);

            $result = $method->invoke($runner, $testImagePath);

            $this->assertStringStartsWith('data:image/png;base64,', $result);
            $this->assertStringContainsString('iVBORw0KGgo', $result); // PNG header in base64
        } finally {
            // Clean up
            if (file_exists($testImagePath)) {
                unlink($testImagePath);
            }
        }
    }

    public function test_get_ai_params_with_valid_image()
    {
        // Create a simple test image
        $testImagePath = sys_get_temp_dir() . '/test_image_params.png';
        $image = imagecreate(10, 10);
        $white = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $white);
        imagepng($image, $testImagePath);
        imagedestroy($image);

        try {
            $request = $this->createMockRequest($testImagePath);
            $runner = new OpenAiRequestRunner($request);

            $params = $runner->getAiParams();

            $this->assertArrayHasKey('messages', $params);
            $this->assertCount(2, $params['messages']);

            // User message should be array with text and image content
            $this->assertEquals('user', $params['messages'][1]['role']);
            $this->assertIsArray($params['messages'][1]['content']);
            $this->assertCount(2, $params['messages'][1]['content']);

            // Check text content
            $this->assertEquals('text', $params['messages'][1]['content'][0]['type']);
            $this->assertEquals('Test user prompt', $params['messages'][1]['content'][0]['text']);

            // Check image content
            $this->assertEquals('image_url', $params['messages'][1]['content'][1]['type']);
            $this->assertArrayHasKey('image_url', $params['messages'][1]['content'][1]);
            $this->assertArrayHasKey('url', $params['messages'][1]['content'][1]['image_url']);
            $this->assertStringStartsWith('data:image/png;base64,', $params['messages'][1]['content'][1]['image_url']['url']);
        } finally {
            // Clean up
            if (file_exists($testImagePath)) {
                unlink($testImagePath);
            }
        }
    }
}
