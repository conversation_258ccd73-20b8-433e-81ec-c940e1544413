<?php

namespace Tests\Unit;

use App\Models\File;
use Tests\TestCase;

class FileImageTest extends TestCase
{
    public function test_is_image_returns_true_for_image_extensions()
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];

        foreach ($imageExtensions as $extension) {
            $file = new File(['location' => "test/path/image.$extension"]);
            $this->assertTrue($file->isImage(), "Failed for extension: $extension");

            // Test uppercase extensions
            $fileUpper = new File(['location' => 'test/path/image.' . strtoupper($extension)]);
            $this->assertTrue($fileUpper->isImage(), 'Failed for uppercase extension: ' . strtoupper($extension));
        }
    }

    public function test_is_image_returns_false_for_non_image_extensions()
    {
        $nonImageExtensions = ['pdf', 'doc', 'docx', 'txt', 'csv', 'xlsx', 'mp4', 'mp3'];

        foreach ($nonImageExtensions as $extension) {
            $file = new File(['location' => "test/path/document.$extension"]);
            $this->assertFalse($file->isImage(), "Failed for extension: $extension");
        }
    }

    public function test_is_image_returns_false_for_no_location()
    {
        $file = new File(['location' => null]);
        $this->assertFalse($file->isImage());

        $file = new File(['location' => '']);
        $this->assertFalse($file->isImage());
    }

    public function test_is_image_returns_false_for_no_extension()
    {
        $file = new File(['location' => 'test/path/filename_without_extension']);
        $this->assertFalse($file->isImage());
    }

    public function test_is_image_handles_complex_paths()
    {
        $file = new File(['location' => 'complex/path/with.dots/in.folder/image.jpg']);
        $this->assertTrue($file->isImage());

        $file = new File(['location' => 'path/with-dashes_and_underscores/image.PNG']);
        $this->assertTrue($file->isImage());
    }
}
