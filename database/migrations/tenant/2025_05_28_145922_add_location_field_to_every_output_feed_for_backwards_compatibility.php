<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection('tenant')->statement(<<<'SQL'
        UPDATE integrations
        SET job_ad_fields = job_ad_fields || '"location"'
        WHERE remote_type = 'feed' AND (jsonb_typeof(job_ad_fields) = 'array' AND NOT job_ad_fields @> '["location"]');
        SQL);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection('tenant')->statement(<<<'SQL'
        UPDATE integrations
        SET job_ad_fields = (
          SELECT jsonb_agg(elem)
          FROM jsonb_array_elements(job_ad_fields) AS elem
          WHERE elem <> '"location"'
        )
        WHERE remote_type = 'feed'
          AND jsonb_typeof(job_ad_fields) = 'array'
          AND job_ad_fields @> '["location"]';
        SQL);
    }
};
