<template>
    <b-form-select
        v-model="selected"
        :options="selectOptions"
    ></b-form-select>
</template>

<script lang="ts">
import Vue from "vue";
import axios from "axios";
import {BButton, BFormSelect, BInputGroup, BInputGroupAppend, BSpinner} from "bootstrap-vue";

export default Vue.extend({
    components: {
        BInputGroup,
        BFormSelect,
        BButton,
        BInputGroupAppend,
        BSpinner,
    },
    data() {
        return {
            templates: [] as Template[],
            loading: false as boolean,
            selected: null,
        };
    },
    props: {
        type: {},
    },
    mounted() {
        this.loadTemplates();
    },
    methods: {
        loadTemplates() {
            this.loading = true;
            axios.get(`/templates?type=${this.type}`).then(res => {
                this.templates = res.data;
                this.loading = false;
            });
        },
    },
    watch: {
        selected(v: number | null) {
            if (v) {
                this.$emit(
                    "template",
                    this.templates.find(t => t.id === v)
                );
            }
        },
    },
    computed: {
        selectOptions: {
            get(): BSelectOption[] {
                return this.templates
                    .map(t => {
                        return {
                            value: t.id,
                            text: t.name + (t.team ? ` (${t.team.name})` : ""),
                        };
                    })
                    .concat([{value: null, text: this.$i18n.t("Select a template").toString()}]);
            },
            set() {},
        },
    },
});
</script>

<style scoped></style>
