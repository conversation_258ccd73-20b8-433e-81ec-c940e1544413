<template>
    <settings-page-card-layout>
        <template #settingsHeader>
            <Link
                :href="route('templates.create')"
                class="btn btn-primary btn-sm"
            >
                <i class="tdi td-plus mr-2"></i>
                {{ $t("Create template") }}
            </Link>
        </template>
        <div>
            <div class="mx-n4 mt-n3 px-4 py-25 mb-3 border-bottom d-flex align-items-center gap-2">
                <checkbox-dropdown
                    v-model="filters.template_types"
                    :options="templateTypes"
                    :text="$t('Type')"
                    icon="td-filter"
                ></checkbox-dropdown>
                <checkbox-dropdown
                    v-model="filters.team_ids"
                    :options="teamsOptions"
                    :text="$t('Team')"
                    v-if="$rlSettings.features.teams"
                    icon="td-users-group"
                ></checkbox-dropdown>
            </div>

            <empty-state
                v-if="templates.length === 0"
                class="mt-5"
                :title="$t('No templates yet')"
                :description="
                    $t('If you have filters applied, check that you have selected the correct template type.')
                "
            ></empty-state>

            <table
                v-else
                class="table table-td"
            >
                <thead>
                    <tr>
                        <th>{{ $t("Name") }}</th>
                        <th>{{ $t("Owner") }}</th>
                        <th>{{ $t("Type") }}</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="template in templates"
                        :key="template.id"
                    >
                        <td>
                            <Link :href="route('templates.edit', template.id)">
                                {{ template.name }}
                            </Link>
                        </td>
                        <td :class="{'font-weight-bold': template.user_id === $rlSettings.user.id}">
                            <template v-if="template.team"> {{ template.team.name }} |</template>
                            {{ template.user_id ? template.user.name : $t("All users") }}
                        </td>
                        <td>{{ templateTypes.find(t => t.value === template.template_type).label }}</td>
                        <td class="text-right">
                            <div class="d-flex align-items-center justify-content-end gap-2">
                                <Link
                                    :href="route('templates.edit', template.id)"
                                    class="btn btn-white btn-sm"
                                >
                                    {{ $t("Edit") }}
                                </Link>
                                <button
                                    class="btn btn-white-danger btn-sm"
                                    @click="confirmDelete(template)"
                                >
                                    {{ $t("Delete") }}
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <b-modal
                v-model="showDeleteModal"
                :title="$t('Delete Template')"
                @ok="deleteTemplate"
            >
                <p>{{ $t("Are you sure you want to delete this template?") }}</p>
            </b-modal>
        </div>
    </settings-page-card-layout>
</template>

<script>
import {defineComponent} from "vue";
import {BDropdown, BDropdownItem, BModal} from "bootstrap-vue";
import EmptyState from "../../common/EmptyState.vue";
import SettingsPageLayout from "../../settings/components/SettingsPageLayout.vue";
import SettingsPageCardLayout from "../../settings/components/SettingsPageCardLayout.vue";
import CheckboxDropdown from "../../common/CheckboxDropdown.vue";

export default defineComponent({
    components: {
        CheckboxDropdown,
        SettingsPageCardLayout,
        BDropdown,
        BDropdownItem,
        BModal,
        EmptyState,
    },
    layout: SettingsPageLayout,

    props: {
        templates: {
            type: Array,
            required: true,
        },
        templateTypes: {
            type: Array,
            required: true,
        },
        teamsOptions: {
            type: Array,
            required: true,
        },
        title: {
            type: String,
            required: true,
        },
        filters: {
            type: Object,
            required: true,
        },
    },

    data() {
        return {
            showDeleteModal: false,
            templateToDelete: null,
        };
    },

    watch: {
        filters: {
            handler() {
                this.$inertia.visit(`/settings/templates`, {
                    preserveState: true,
                    data: this.filters,
                    only: ["templates"],
                });
            },
            deep: true,
        },
    },

    computed: {},

    methods: {
        confirmDelete(template) {
            this.templateToDelete = template;
            this.showDeleteModal = true;
        },

        deleteTemplate() {
            if (this.templateToDelete) {
                this.$inertia.delete(this.route("templates.destroy", this.templateToDelete.id));
            }
        },
    },
});
</script>
