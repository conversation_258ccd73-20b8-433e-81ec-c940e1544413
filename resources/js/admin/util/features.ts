export interface DisplayFeature {
    key: string;
    label: string;
    suggestedPrice: number;
}

export const getDisplayFeatures = (): DisplayFeature[] => {
    return [
        {key: "video_interviews", label: "Video interviews", suggestedPrice: 1200},
        {key: "requisitions", label: "Requisitions", suggestedPrice: 1200},
        {key: "scorecards", label: "Scorecards", suggestedPrice: 1200},
        {key: "career_pages", label: "Career pages", suggestedPrice: 1200},
        {key: "references", label: "References", suggestedPrice: 1200},
        {key: "credential_validity", label: "Credential validities", suggestedPrice: 1200},
        {key: "ai_transcripts", label: "AI transcripts", suggestedPrice: 1200},
        {key: "teams", label: "Teams", suggestedPrice: 2000},
        {key: "audit_log", label: "Audit log", suggestedPrice: 1000},
        {key: "enable_ai", label: "AI", suggestedPrice: 1000},
        {key: "sso", label: "SSO", suggestedPrice: 1000},
        {key: "favicon", label: "Favicon", suggestedPrice: 1000},
        {key: "ai_candidate_screening", label: "Screening", suggestedPrice: 2000},
    ];
};
