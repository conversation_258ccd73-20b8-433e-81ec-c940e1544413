<script lang="ts">
import {defineComponent} from "vue";
import MainLayout from "../../common/MainLayout.vue";
import ProjectUsersModal from "./ProjectUsersModal.vue";
import ProjectChildrenModal from "./ProjectChildrenModal.vue";
import ProjectStatus from "./ProjectStatus.vue";
import InlineEditable from "../../common/InlineEditable.vue";
import UserAvatar from "../../common/UserAvatar.vue";
import SideMenuItem from "../../common/components/SideMenuItem.vue";
import SideMenu from "../../common/components/SideMenu.vue";
import {BPopover} from "bootstrap-vue";
import ProjectDeadline from "./ProjectDeadline.vue";
import NpsTable from "./NpsTable.vue";
import {Link} from "@inertiajs/vue2";
import {LoadedShowProject} from "../util";

export default defineComponent({
    name: "ProjectLayout",
    components: {
        NpsTable,
        ProjectDeadline,
        SideMenu,
        SideMenuItem,
        BPopover,
        UserAvatar,
        InlineEditable,
        ProjectStatus,
        ProjectChildrenModal,
        ProjectUsersModal,
        MainLayout,
        Link,
    },
    props: {
        project: {
            type: Object as () => LoadedShowProject,
            required: true,
        },
    },
    data() {
        return {
            showHelpers: {
                members: false,
                status: false,
                stages: false,
            },
            showAddUsersModal: false,
            showStatsModal: false,
            showNpsResultsModal: false,
            showJobAdModal: false,
            showTemplateProjectsModal: false,
        };
    },
    methods: {
        showProjectUsersModal() {
            if (this.project.is_template) {
                return;
            }
            if (
                this.$rlSettings.user.role !== "limited" &&
                this.$rlSettings.controls.projects.update &&
                !(this.project.is_template && this.$rlSettings.user.role !== "admin")
            ) {
                this.showAddUsersModal = true;
                this.hideHelper("members");
            }
        },
        showHelper(helper: "members" | "status" | "stages") {
            this.showHelpers[helper] = true;
        },
        hideHelper(helper: "members" | "status" | "stages") {
            this.showHelpers[helper] = false;
        },
    },
    computed: {
        readOnly(): boolean {
            return this.project.status === 3 || this.project.status === 4;
        },
        projectUsers() {
            // Sort managers to the first position, others to the end
            return [...(this.project.users ?? [])].sort((a: User, b: User) => {
                if (a.pivot.is_manager) return -1;
                if (b.pivot.is_manager) return 1;
                return 0;
            });
        },
    },
});
</script>

<template>
    <main-layout>
        <template #topSection>
            <div class="container-fluid">
                <div
                    class="alert alert-warning mt-3 mb-0 d-flex align-items-baseline"
                    v-if="project.is_template"
                >
                    <i class="tdi td-info-circle mr-2"></i>
                    <div>
                        <strong>{{ $t("You are editing a project template.") }}</strong>
                        <p class="text-sm mb-0">
                            {{ $t("Changes made here will not affect existing projects created from this template.") }}
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="d-flex align-items-center mt-3 mb-3">
                            <Link
                                :href="route('projects.index')"
                                class="btn btn-circle btn-white btn-lg mr-3"
                                v-b-tooltip.bottom="$t('Back to projects list')"
                            >
                                <i class="fas fa-chevron-left fa-xs"></i>
                            </Link>
                            <h1 class="m-0 mr-3 text-dark">
                                <inline-editable
                                    :model="project"
                                    field="position_name"
                                    :disabled="!$rlSettings.controls.projects.update"
                                    model-name="App\Models\Project"
                                />
                            </h1>
                            <div
                                class="d-none d-sm-inline-flex align-items-center avatar-group mr-25"
                                v-if="project.manager"
                            >
                                <user-avatar
                                    v-if="!project.is_template"
                                    v-for="u in projectUsers"
                                    avatar-class="avatar--borderless cursor-pointer"
                                    :user="u"
                                    :key="u.id + JSON.stringify(u.pivot)"
                                    :tooltip-extra="
                                        u.pivot.is_manager
                                            ? project.is_template
                                                ? $t('Template owner')
                                                : $t('Project manager')
                                            : ''
                                    "
                                />

                                <button
                                    id="manage-project-access-button"
                                    class="btn btn-circle btn-white ml-n3"
                                    :class="{'pulse-success': project.users.length === 0}"
                                    v-b-tooltip.bottom="$t('Manage project access')"
                                    @click="showProjectUsersModal"
                                    :disabled="!$rlSettings.controls.projects.update"
                                    v-if="$rlSettings.user.role !== 'limited' && !project.is_template"
                                >
                                    <i class="tdi td-add-user"></i>
                                </button>
                                <b-popover
                                    variant="warning"
                                    target="manage-project-access-button"
                                    :show.sync="showHelpers['members']"
                                    placement="bottomright"
                                    triggers=""
                                >
                                    <div class="d-flex">
                                        <span class="text-md font-weight-medium">{{
                                            $t(
                                                "Project users and teams can now be added and removed in one convenient place."
                                            )
                                        }}</span>
                                        <div class="flex-shrink-0">
                                            <i
                                                class="tdi td-close cursor-pointer"
                                                @click="hideHelper('members')"
                                            ></i>
                                        </div>
                                    </div>
                                </b-popover>
                            </div>
                            <div
                                class="d-none d-sm-flex align-items-baseline gap-3"
                                v-if="!project.is_template"
                            >
                                <Link
                                    class="badge badge-pill badge-white text-sm border"
                                    v-if="project.template_id"
                                    :href="`/projects/${project.template_id}`"
                                    target="_blank"
                                    v-b-tooltip.bottom.hover="$t('View template')"
                                >
                                    <i class="tdi td-puzzle-piece td-sm"></i>
                                </Link>
                                <project-status
                                    id="project-status-badge"
                                    :project="project"
                                    @update="$inertia.reload()"
                                    @opened="hideHelper('status')"
                                ></project-status>
                                <b-popover
                                    variant="warning"
                                    target="project-status-badge"
                                    :show.sync="showHelpers['status']"
                                    placement="bottomright"
                                    triggers=""
                                >
                                    <div class="d-flex">
                                        <span class="text-md font-weight-medium">{{
                                            $t(
                                                "Project status, deadlines, warranties and other dates can be found here now."
                                            )
                                        }}</span>
                                        <div class="flex-shrink-0">
                                            <i
                                                class="tdi td-close cursor-pointer"
                                                @click="hideHelper('status')"
                                            ></i>
                                        </div>
                                    </div>
                                </b-popover>
                                <project-deadline
                                    text-class="text-center text-sm text-black-50 clickable"
                                    :show-nps="false"
                                    :project="project"
                                ></project-deadline>
                                <a
                                    v-if="project.nps_score !== null"
                                    href="#"
                                    @click.prevent="showNpsResultsModal = true"
                                    class="text-sm text-black-50"
                                    v-b-tooltip.bottom.hover="$t('View cNPS results')"
                                >
                                    cNPS: {{ project.nps_score }}
                                </a>
                                <small v-if="readOnly">{{ $t("This project is finished and read-only.") }}</small>
                            </div>
                            <div
                                class="d-none d-sm-flex align-items-baseline gap-3"
                                v-if="project.is_template"
                            >
                                <span
                                    class="text-sm cursor-pointer"
                                    @click="showTemplateProjectsModal = true"
                                >
                                    {{
                                        $tc(
                                            "1 project from this template|{count} projects from this template",
                                            project.children_count,
                                            {count: project.children_count}
                                        )
                                    }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <project-users-modal
                v-model="showAddUsersModal"
                :project="project"
                @update="$inertia.reload()"
            ></project-users-modal>
            <project-children-modal
                :title="$t('Projects using {templateName} as template', {templateName: project.position_name})"
                v-model="showTemplateProjectsModal"
                :project="project"
            ></project-children-modal>
        </template>
        <div class="d-flex mt-2 w-100">
            <side-menu class="mr-3">
                <side-menu-item
                    id="board-button"
                    icon="td-view-column"
                    :text="$t('Board')"
                    :link="`/projects/${project.id}`"
                    @click="hideHelper('stages')"
                ></side-menu-item>
                <b-popover
                    variant="warning"
                    target="board-button"
                    custom-class="shadow-lg"
                    :show.sync="showHelpers['stages']"
                    placement="rightbottom"
                    triggers=""
                >
                    <div class="d-flex">
                        <span class="text-md font-weight-medium">{{
                            $t(
                                "Everything related to project stages can now be edited directly on the candidates board."
                            )
                        }}</span>
                        <div class="flex-shrink-0">
                            <i
                                class="tdi td-close cursor-pointer"
                                @click="hideHelper('stages')"
                            ></i>
                        </div>
                    </div>
                </b-popover>
                <side-menu-item
                    icon="td-add-to-cloud"
                    :text="$t('Job ads')"
                    :count="project.structured_job_ads_count"
                    :link="`/projects/${project.id}/job-ads`"
                    v-if="!project.is_template"
                ></side-menu-item>
                <side-menu-item
                    icon="td-chart-square"
                    :text="$t('Statistics')"
                    :link="`/projects/${project.id}/statistics`"
                    v-if="$rlSettings.user.role !== 'limited' && !project.is_template"
                ></side-menu-item>
                <side-menu-item
                    icon="td-folder"
                    :text="project.is_template ? $t('Template log') : $t('Project log')"
                    :link="`/projects/${project.id}/log`"
                    :count="project.logs_count + (project.has_requisition ? 1 : 0)"
                ></side-menu-item>
                <side-menu-item
                    icon="td-edit"
                    :text="project.is_template ? $t('Edit template') : $t('Edit project')"
                    :link="`/projects/${project.id}/edit`"
                    v-if="
                        $rlSettings.user.role !== 'limited' &&
                        !(project.is_template && $rlSettings.user.role !== 'admin')
                    "
                ></side-menu-item>
                <side-menu-item
                    icon="td-filter"
                    :text="$t('Screening')"
                    :count="project.applications_pending_screening_count"
                    :link="`/projects/${project.id}/screening`"
                    v-if="$rlSettings.user.role !== 'limited' && !project.is_template"
                ></side-menu-item>
                <side-menu-item
                    icon="td-direction"
                    :text="$t('Actions')"
                    :link="`/projects/${project.id}/actions`"
                    v-if="$rlSettings.user.role !== 'limited'"
                ></side-menu-item>
            </side-menu>
            <slot></slot>
        </div>
        <b-modal
            size="lg"
            lazy
            :title="$t('cNPS responses')"
            v-model="showNpsResultsModal"
            hide-footer
            header-class="border-bottom d-flex align-items-center"
        >
            <template #modal-header="{close}">
                <h2 class="mb-0">{{ $t("cNPS responses") }}</h2>
                <div class="ml-auto d-flex align-items-center">
                    <button
                        class="btn btn-sm btn-white btn-circle"
                        @click="close()"
                    >
                        <i class="tdi td-close"></i>
                    </button>
                </div>
            </template>
            <nps-table :for-project="project"></nps-table>
        </b-modal>
    </main-layout>
</template>
