<style>
.sentence-with-selects .btn-transparent {
    padding: 0 !important;
    font-size: 0.925rem;
    font-weight: 700 !important;
}
</style>
<template>
    <div class="flex-grow-1 pr-4">
        <div class="row">
            <div class="col-8">
                <div class="card">
                    <div class="card-header border-bottom-0 pb-2 d-flex align-items-center justify-content-between">
                        <h3 class="font-weight-medium mb-0">
                            {{ $t("Candidate Screening for {position}", {position: project.position_name}) }}
                        </h3>
                        <div class="d-flex align-items-center gap-2">
                            <a
                                class="btn btn-info btn-sm"
                                href="https://support.teamdash.com/en/"
                                target="_blank"
                            >
                                <i class="tdi td-info-circle mr-1"></i>
                                {{ $t("Help Center") }}
                            </a>
                        </div>
                    </div>
                    <div
                        class="card-body px-25 py-2"
                        style="overflow-x: auto"
                    >
                        <td-table
                            v-if="screeningCriteria.length"
                            :items="sortedApplications"
                            :fields="tableFields"
                            :tbody-tr-class="getRowClass"
                        >
                            <template #cell(candidate.name)="row">
                                <div class="d-flex align-items-center justify-content-between gap-2">
                                    <a
                                        :href="`/candidates/${row.item.candidate.id}`"
                                        target="_blank"
                                        class="font-weight-medium"
                                        :class="{
                                            'text-danger': row.item.screening_criterion_responses?.some(
                                                r => !!r.error_at
                                            ),
                                        }"
                                    >
                                        {{ row.item.candidate.name }}
                                    </a>
                                    <div
                                        v-if="row.item.screening_criterion_responses?.some(r => !!r.error_at)"
                                        v-b-tooltip.bottom.hover="
                                            row.item.screening_criterion_responses[0].error_message
                                        "
                                    >
                                        <i class="tdi td-alert-circle text-danger"></i>
                                    </div>
                                    <hover-file-preview
                                        v-if="row.item.candidate.last_cv"
                                        :url="row.item.candidate.last_cv.url"
                                        :file-id="row.item.candidate.last_cv.id"
                                    >
                                        <i class="fas fa-download"></i> CV
                                    </hover-file-preview>
                                </div>
                            </template>
                            <template
                                :slot="`cell(criterion_${index})`"
                                slot-scope="row"
                                v-for="(criterion, index) in screeningCriteria"
                            >
                                <i
                                    v-b-tooltip="getResult(row.item, criterion)?.details"
                                    class="tdi text-lg"
                                    :class="{
                                        'td-check-circle': getResult(row.item, criterion)?.result === 'match',
                                        'td-alert-triangle': getResult(row.item, criterion)?.result === 'partial_match',
                                        'td-alert-circle': getResult(row.item, criterion)?.result === 'no_match',
                                        'text-success': getResult(row.item, criterion)?.result === 'match',
                                        'text-warning': getResult(row.item, criterion)?.result === 'partial_match',
                                        'text-danger': getResult(row.item, criterion)?.result === 'no_match',
                                    }"
                                ></i>
                            </template>
                            <template #cell(stage)="row">
                                <td-single-select
                                    :options="
                                        project.stages.map(s => ({
                                            value: s.id,
                                            label: s.name,
                                        }))
                                    "
                                    none-selected-text="Move to"
                                    :value="row.item.stage_id"
                                    @input="
                                        moveApplicationsToStage(
                                            [row.item],
                                            project.stages.find(s => s.id === $event)
                                        )
                                    "
                                ></td-single-select>
                            </template>
                        </td-table>
                        <empty-state
                            :title="'uh-oh'"
                            v-else
                            center
                        >
                            {{ $t("Please add screening criteria to get started.") }}
                        </empty-state>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div
                    class="card sticky-top"
                    style="z-index: 0"
                >
                    <div class="border-bottom-0 pb-0">
                        <b-tabs nav-class="nav nav-tabs text-sm px-3">
                            <b-tab
                                :title="$t('Screening')"
                                active
                            >
                                <b-overlay
                                    :show="
                                        !$rlSettings.features.ai_candidate_screening || !$rlSettings.features.enable_ai
                                    "
                                >
                                    <template #overlay>
                                        <div class="d-flex flex-column align-items-center gap-2 text-center">
                                            <i class="fas fa-crown text-muted text-lg"></i>
                                            <div v-if="!$rlSettings.features.enable_ai">
                                                {{
                                                    $t(
                                                        "AI features must be enabled for the AI screener to work. Please contact your account manager."
                                                    )
                                                }}
                                            </div>
                                            <div v-else-if="!$rlSettings.features.ai_candidate_screening">
                                                {{
                                                    $t(
                                                        "Please contact your account manager to enable AI candidate screening."
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </template>
                                    <div class="card-body px-25 pt-3">
                                        <div class="mb-3">
                                            <div v-if="!project.screening_criteria.length">
                                                <button
                                                    class="btn btn-primary mt-2"
                                                    @click="generateScreeningCriteria"
                                                    v-if="!screeningCriteria.length"
                                                    :disabled="isGenerating"
                                                >
                                                    <b-spinner
                                                        small
                                                        v-if="isGenerating"
                                                        class="mr-2"
                                                    ></b-spinner>
                                                    {{
                                                        isGenerating
                                                            ? $t("Generating...")
                                                            : $t("Generate Screening Criteria")
                                                    }}
                                                </button>
                                            </div>

                                            <div
                                                class="d-flex align-items-center gap-2"
                                                v-else
                                            >
                                                <div class="d-flex gap-2">
                                                    <button
                                                        class="btn btn-primary"
                                                        :disabled="
                                                            !screeningCriteria.length ||
                                                            runningScreener ||
                                                            clearingResults
                                                        "
                                                        @click="runScreener()"
                                                    >
                                                        <template v-if="runningScreener">
                                                            <b-spinner
                                                                small
                                                                class="mr-2"
                                                            ></b-spinner>
                                                            {{ $t("Running...") }}
                                                        </template>
                                                        <template v-else>
                                                            {{ $t("Run screener") }}
                                                        </template>
                                                    </button>
                                                    <button
                                                        class="btn btn-outline-secondary"
                                                        :disabled="
                                                            !hasScreeningResults || runningScreener || clearingResults
                                                        "
                                                        @click="clearScreenerResults"
                                                    >
                                                        <template v-if="clearingResults">
                                                            <b-spinner
                                                                small
                                                                class="mr-2"
                                                            ></b-spinner>
                                                            {{ $t("Clearing...") }}
                                                        </template>
                                                        <template v-else>
                                                            {{ $t("Clear screener results") }}
                                                        </template>
                                                    </button>
                                                </div>
                                            </div>

                                            <div
                                                v-if="showValidationErrors && validationErrors.length > 0"
                                                class="mt-3"
                                            >
                                                <div class="alert alert-danger">
                                                    <h5 class="alert-heading">
                                                        {{ $t("Unsuitable Criteria Detected") }}
                                                    </h5>
                                                    <p>
                                                        {{
                                                            $t(
                                                                "The following criteria may be discriminatory or otherwise unsuitable and should be reviewed:"
                                                            )
                                                        }}
                                                    </p>
                                                    <ul class="mb-0">
                                                        <li
                                                            v-for="(error, index) in validationErrors.filter(
                                                                e => e.is_unsuitable
                                                            )"
                                                            :key="index"
                                                            class="mb-2"
                                                        >
                                                            <div
                                                                class="d-flex justify-content-between align-items-start"
                                                            >
                                                                <div>
                                                                    <strong>{{ error.criterion_text }}</strong>
                                                                    <p
                                                                        v-if="error.reason"
                                                                        class="mb-0 mt-1 text-sm"
                                                                    >
                                                                        {{ error.reason }}
                                                                    </p>
                                                                </div>
                                                                <button
                                                                    class="btn btn-sm btn-outline-danger ml-2"
                                                                    @click="deleteCriterionById(error.criterion_id)"
                                                                    :disabled="deletingCriterion"
                                                                    v-b-tooltip="$t('Remove criterion')"
                                                                >
                                                                    <i class="tdi td-trash"></i>
                                                                </button>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <div class="mt-3 d-flex justify-content-center">
                                                        <button
                                                            class="btn btn-outline-danger"
                                                            @click="runScreenerWithOverride()"
                                                            :disabled="runningScreener"
                                                        >
                                                            <b-spinner
                                                                small
                                                                v-if="runningScreener"
                                                                class="mr-2"
                                                            ></b-spinner>
                                                            <i
                                                                class="tdi td-alert-triangle mr-2"
                                                                v-else
                                                            ></i>
                                                            {{ $t("Run screener anyway") }}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <div
                                                class="list-group"
                                                v-if="screeningCriteria.length"
                                            >
                                                <div
                                                    v-for="(criterion, index) in screeningCriteria"
                                                    :key="index"
                                                    class="list-group-item"
                                                >
                                                    <div
                                                        class="d-flex gap-2 justify-content-between align-items-center"
                                                    >
                                                        <div class="flex-grow-1">
                                                            <input
                                                                type="text"
                                                                v-model="criterion.criterion"
                                                                class="form-control w-100 form-control-sm"
                                                                :class="{
                                                                    'is-invalid': isDiscriminatoryCriterion(
                                                                        criterion.id
                                                                    ),
                                                                }"
                                                                @change="updateCriterionText(criterion)"
                                                            />
                                                            <div
                                                                v-if="isDiscriminatoryCriterion(criterion.id)"
                                                                class="invalid-feedback"
                                                            >
                                                                {{ getDiscriminatoryReason(criterion.id) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mt-2 d-flex align-items-center gap-2">
                                                        <styled-checkbox
                                                            v-model="criterion.is_required"
                                                            :true-value="true"
                                                            :false-value="false"
                                                            size="sm"
                                                            @change="updateCriterionRequired(criterion)"
                                                        >
                                                            {{ $t("Required") }}
                                                        </styled-checkbox>
                                                        <button
                                                            class="btn btn-sm btn-white ml-auto mr-0"
                                                            @click="deleteCriterionById(criterion.id)"
                                                        >
                                                            {{ $t("Delete") }}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mt-4 d-flex align-items-center gap-2">
                                                <input
                                                    type="text"
                                                    class="form-control"
                                                    v-model="newCriterion"
                                                    @keyup.enter="addCriterion"
                                                    :placeholder="
                                                        project.screening_criteria?.length
                                                            ? $t('Add new criterion')
                                                            : $t('Or add a criterion manually')
                                                    "
                                                />
                                                <button
                                                    class="btn btn-primary"
                                                    @click="addCriterion"
                                                >
                                                    {{ $t("Add") }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </b-overlay>
                            </b-tab>
                            <b-tab
                                :title="$t('Sorting')"
                                :disabled="!hasScreeningResults"
                            >
                                <div class="p-3">
                                    <div class="sentence-with-selects">
                                        {{
                                            $t("Move candidates with a score", {
                                                count: selectedApplications.length,
                                            })
                                        }}
                                        <td-single-select
                                            class="px-0"
                                            variant="transparent"
                                            v-model="minScoreDirection"
                                            :options="[
                                                {value: 'above', label: $t('higher than')},
                                                {value: 'below', label: $t('lower than')},
                                            ]"
                                        ></td-single-select>
                                        <strong class="text-success">
                                            {{ formattedMinScore }}
                                        </strong>

                                        <span>{{ $t("to stage") }} </span>
                                        <td-single-select
                                            v-model="minScoreStageId"
                                            variant="transparent"
                                            :options="
                                                project.stages.map(s => ({
                                                    value: s.id,
                                                    label: s.name,
                                                }))
                                            "
                                        ></td-single-select>
                                    </div>

                                    <div class="d-flex align-items-center gap-2 mb-2 mt-2">
                                        Score:
                                        <input
                                            type="range"
                                            v-model="minScore"
                                            class="w-100"
                                            min="0"
                                            max="100"
                                            step="1"
                                        />
                                    </div>

                                    <button
                                        class="btn btn-primary"
                                        :disabled="movingApplications || selectedApplications.length === 0"
                                        @click="
                                            moveApplicationsToStage(
                                                selectedApplications,
                                                project.stages.find(s => s.id === minScoreStageId)
                                            )
                                        "
                                    >
                                        <b-spinner
                                            small
                                            class="mr-2"
                                            v-if="movingApplications"
                                        ></b-spinner>
                                        {{ $t("Move {count} candidates", {count: selectedApplications.length}) }}
                                    </button>
                                </div>
                            </b-tab>
                        </b-tabs>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import {defineComponent} from "vue";
import {BOverlay, BSpinner, BTab, BTabs} from "bootstrap-vue";
import {PropType} from "vue/types/options";
import {LoadedShowProject} from "../util";
import EmptyState from "../../common/EmptyState.vue";
import ProjectLayout from "../components/ProjectLayout.vue";
import TdTable from "../../common/library/TdTable.vue";
import HoverFilePreview from "../../common/components/HoverFilePreview.vue";
import axios from "axios";
import StyledCheckbox from "../../common/StyledCheckbox.vue";
import TdSingleSelect from "../../common/library/TdSingleSelect.vue";
import {CATEGORY_DROPOUT, CATEGORY_INTERVIEWS} from "../../common/stageCategories";

interface CriterionValidationError {
    criterion_id: number;
    criterion_text: string;
    is_unsuitable: boolean;
    reason: string | null;
}

export default defineComponent({
    name: "ProjectScreeningView",
    layout: ProjectLayout,
    components: {
        TdSingleSelect,
        StyledCheckbox,
        HoverFilePreview,
        TdTable,
        EmptyState,
        BSpinner,
        BTabs,
        BTab,
        BOverlay,
    },
    props: {
        project: {
            type: Object as PropType<LoadedShowProject>,
            required: true,
        },
        applications: {
            type: Array as PropType<Application[]>,
            required: true,
            default: () => [],
        },
    },
    data() {
        return {
            loading: false,
            isGenerating: false,
            newCriterion: "",
            remoteUpdated: false,
            runningScreener: false,
            clearingResults: false,
            validationErrors: [] as CriterionValidationError[],
            showValidationErrors: false,
            deletingCriterion: false,
            minScore: 50 as number | null, // Default to 50% instead of null
            minScoreDirection: "above" as "above" | "below",
            minScoreStageId: this.project.stages.find(s => s.category === CATEGORY_INTERVIEWS)?.id,
            movingApplications: false,
        };
    },
    mounted() {
        analytics.track("Opened project screening");

        // Set up Echo listener for screening updates
        if (this.$echo) {
            console.log("Subscribing to", this.updatesChannelName);
            this.$echo.private(this.updatesChannelName).listen("ProjectScreeningUpdated", (e: any) => {
                console.log("Received screening update", e);
                const application = this.applications.find(a => a.id === e.applicationId);
                if (application) {
                    application.screening_criterion_responses = e.results;
                }

                if (
                    this.applications.every(
                        a => a.screening_criterion_responses && a.screening_criterion_responses.length > 0
                    )
                ) {
                    this.runningScreener = false;
                }
            });
        }
    },
    beforeDestroy() {
        // Clean up Echo listener when component is destroyed
        if (this.$echo) {
            console.log("Leaving channel", this.updatesChannelName);
            this.$echo.leave(this.updatesChannelName);
        }
    },
    computed: {
        screeningCriteria(): ScreeningCriterion[] {
            return this.project.screening_criteria || [];
        },
        tableFields() {
            return [
                {key: "candidate.name", label: "Name"},
                ...this.screeningCriteria.map((criterion, index) => ({
                    key: `criterion_${index}`,
                    label: criterion.criterion,
                })),
                {
                    key: "score",
                    label: "Score",
                    formatter: (value, key, item) => `${this.getScore(item)}%`,
                },
                {
                    key: "stage",
                    label: "Stage",
                },
            ];
        },
        updatesChannelName(): string {
            return `${this.$rlSettings.instance_uuid}.project.${this.project.id}`;
        },

        sortedApplications(): Application[] {
            return this.applications.toSorted((a: Application, b: Application) => {
                // if it doesn't have any responses, put it at the beginning
                if (a.screening_criterion_responses?.length === 0) {
                    return -1;
                }
                if (b.screening_criterion_responses?.length === 0) {
                    return 1;
                }

                return this.getRawScore(b) - this.getRawScore(a);
            });
        },

        hasScreeningResults(): boolean {
            return this.applications.some(application => {
                return (
                    application.screening_criterion_responses && application.screening_criterion_responses.length > 0
                );
            });
        },
        uniqueScores(): number[] {
            return Array.from(new Set(this.sortedApplications.map(a => this.getScore(a)))).toSorted((a, b) => b - a);
        },
        selectedApplications(): Application[] {
            return this.sortedApplications.filter(a => {
                if (this.minScore === null) {
                    return false;
                }

                const score = this.getScore(a);
                return this.minScoreDirection === "above" ? score > this.minScore : score < this.minScore;
            });
        },
        formattedMinScore(): string {
            return this.minScore === null ? "N/A" : `${this.minScore}%`;
        },
    },
    methods: {
        getResult(application: Application, criterion: ScreeningCriterion): ScreeningCriterionResponse | undefined {
            return application.screening_criterion_responses!.find(r => r.screening_criterion_id === criterion.id);
        },
        track(evt) {
            console.log(evt);
        },
        getMaxPossibleScore(): number {
            return this.screeningCriteria.reduce((acc: number, criterion: ScreeningCriterion) => {
                if (criterion.is_required) {
                    return acc + 2;
                } else {
                    return acc + 1;
                }
            }, 0);
        },
        getRawScore(application: Application): number {
            return (
                application.screening_criterion_responses?.reduce((acc: number, m: ScreeningCriterionResponse) => {
                    const criterion = this.screeningCriteria.find(c => c.id === m.screening_criterion_id);

                    if (!criterion) {
                        return acc;
                    }

                    if (m.result === "match" && criterion?.is_required) {
                        return acc + 2;
                    }
                    if (m.result === "match") {
                        return acc + 1;
                    }

                    if (m.result === "no_match" && criterion?.is_required) {
                        return acc - Infinity;
                    }
                    if (m.result === "no_match") {
                        return acc;
                    }

                    if (m.result === "partial_match" && !criterion?.is_required) {
                        return acc + 0.5;
                    }
                    if (m.result === "partial_match" && criterion?.is_required) {
                        return acc - 1;
                    }

                    return acc;
                }, 0) ?? 0
            );
        },
        getScore(application: Application): number {
            const rawScore = this.getRawScore(application);
            const maxScore = this.getMaxPossibleScore();

            if (rawScore === -Infinity || maxScore === 0) {
                return 0;
            }

            return Math.round((rawScore / maxScore) * 100);
        },
        getRowClass(item: Application, type: string) {
            const selectedStageCategory = this.project.stages.find(s => s.id === this.minScoreStageId)?.category;
            const rowClass = selectedStageCategory === CATEGORY_DROPOUT ? "bg-danger" : "bg-success-lighter";

            if (type !== "row") {
                return [];
            }

            return [this.selectedApplications.includes(item) ? rowClass : null];
        },
        async generateScreeningCriteria() {
            this.isGenerating = true;
            try {
                await axios.post(`/projects/${this.project.id}/generateAndPersistCriteria`);
                analytics.track("Screening criteria generated");
                this.$inertia.reload({only: ["project"]});
            } catch (error) {
                console.log(error);
                // toast
                this.$bvToast.toast(error.response?.data?.error ?? this.$t("Failed to generate screening criteria"), {
                    title: this.$t("Error"),
                    variant: "danger",
                    solid: true,
                });
            } finally {
                this.isGenerating = false;
            }
        },
        async addCriterion() {
            if (!this.newCriterion) {
                return;
            }
            try {
                await axios.post(`/projects/${this.project.id}/screening-criteria`, {
                    criterion: this.newCriterion,
                    is_required: false,
                });
                this.newCriterion = "";
                this.$inertia.reload({only: ["project"]});
            } catch (error) {
                console.error("Error adding criterion:", error);
            }
        },
        async loadPersistedResponses() {
            this.$inertia.reload({only: ["applications"]});
        },
        async updateCriterionRequired(criterion: ScreeningCriterion) {
            try {
                await axios.put(`/screening-criteria/${criterion.id}`, {
                    is_required: criterion.is_required,
                });
            } catch (error) {
                console.error("Error updating criterion:", error);
            }
        },
        async updateCriterionText(criterion: ScreeningCriterion) {
            try {
                await axios.put(`/screening-criteria/${criterion.id}`, {
                    criterion: criterion.criterion,
                });
            } catch (error) {
                console.error("Error updating criterion text:", error);
            }
        },
        runScreener(override = false) {
            this.runningScreener = true;
            this.validationErrors = [];
            this.showValidationErrors = false;

            analytics.track("Screening run");

            const applicationsWithoutResponses = this.applications.filter(
                a => !a.screening_criterion_responses || a.screening_criterion_responses.length === 0
            );

            let applicationIds = [];

            if (
                applicationsWithoutResponses.length === this.applications.length ||
                applicationsWithoutResponses.length === 0
            ) {
                applicationIds = this.applications.map(a => a.id);
            } else {
                applicationIds = applicationsWithoutResponses.map(a => a.id);
            }

            axios
                .post(`/projects/${this.project.id}/runScreener`, {
                    application_ids: applicationIds,
                    override: override,
                })
                .catch(error => {
                    if (error.response && error.response.status === 422) {
                        // Handle validation errors
                        this.validationErrors = error.response.data.validation_errors || [];
                        this.showValidationErrors = true;

                        // Show an error message
                        this.$bvToast.toast(error.response.data.message, {
                            title: this.$t("Validation Error"),
                            variant: "danger",
                            solid: true,
                        });
                        this.runningScreener = false;
                    } else {
                        console.error("Error running screener:", error);

                        // Show a generic error message
                        this.$bvToast.toast(this.$t("Failed to run screener"), {
                            title: this.$t("Error"),
                            variant: "danger",
                            solid: true,
                        });
                        this.runningScreener = false;
                    }
                });
        },

        runScreenerWithOverride() {
            this.runScreener(true);
        },

        clearScreenerResults() {
            this.clearingResults = true;
            axios
                .post(`/projects/${this.project.id}/clearScreenerResults`)
                .then(({data}) => {
                    this.$inertia.reload({only: ["applications"]});

                    // Show a success message
                    this.$bvToast.toast(this.$t("Screening results cleared"), {
                        title: this.$t("Success"),
                        variant: "success",
                        solid: true,
                    });

                    analytics.track("Screening results cleared");
                })
                .catch(error => {
                    console.error("Error clearing screening results:", error);

                    // Show an error message
                    this.$bvToast.toast(this.$t("Failed to clear screening results"), {
                        title: this.$t("Error"),
                        variant: "danger",
                        solid: true,
                    });
                })
                .finally(() => {
                    this.clearingResults = false;
                });
        },
        moveApplicationsToStage(applications: Application[], stage: Stage) {
            this.movingApplications = true;
            axios
                .post(`/projects/${this.project.id}/commitStageTransitions`, {
                    transitions: applications.map(a => ({
                        application_id: a.id,
                        candidate_id: a.candidate_id,
                        to_stage_id: stage.id,
                    })),
                })
                .then(({data}) => {
                    this.$inertia.reload({only: ["applications", "project"]});
                    this.movingApplications = false;
                    analytics.track("Moved candidates to stage from screening results");
                });
        },

        // Check if a criterion is discriminatory based on validation results
        isDiscriminatoryCriterion(criterionId) {
            if (!this.validationErrors.length) return false;

            return this.validationErrors.some(error => error.criterion_id === criterionId && error.is_unsuitable);
        },

        // Get the reason why a criterion is discriminatory
        getDiscriminatoryReason(criterionId) {
            if (!this.validationErrors.length) return "";

            const error = this.validationErrors.find(
                error => error.criterion_id === criterionId && error.is_unsuitable
            );

            return error ? error.reason : "";
        },

        async deleteCriterionById(criterionId: number) {
            if (!criterionId) return;

            this.deletingCriterion = true;
            try {
                await axios.delete(`/screening-criteria/${criterionId}`);
                this.validationErrors = this.validationErrors.filter(error => error.criterion_id !== criterionId);

                if (!this.validationErrors.some(error => error.is_unsuitable)) {
                    this.showValidationErrors = false;
                }

                this.$inertia.reload({only: ["project", "applications"]});
            } catch (error) {
                console.error("Error deleting criterion:", error);
                this.$bvToast.toast(this.$t("Failed to remove criterion"), {
                    title: this.$t("Error"),
                    variant: "danger",
                    solid: true,
                });
            } finally {
                this.deletingCriterion = false;
            }
        },
    },
});
</script>
