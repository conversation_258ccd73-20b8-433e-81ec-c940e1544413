<script lang="ts">
import Vue, {defineComponent} from "vue";
import MainLayout from "../../common/MainLayout.vue";
import {PropType} from "vue/types/options";
import DashboardIndicator from "../components/statistics/DashboardIndicator.vue";
import CheckboxDropdown from "../../common/CheckboxDropdown.vue";
import StyledCheckbox from "../../common/StyledCheckbox.vue";
import {
    BAlert,
    BButton,
    BCollapse,
    BDropdown,
    BDropdownDivider,
    BDropdownItem,
    BFormInput,
    BInputGroup,
    BInputGroupText,
    BNav,
    BNavItem,
    BOverlay,
    TooltipPlugin,
} from "bootstrap-vue";
import {isEmpty, sortBy, zipObject} from "lodash";
import ProjectsTab from "../views/ProjectsTab.vue";
import {ProjectWithRequisition} from "../index";
import axios, {AxiosResponse} from "axios";
import FormModal from "../../forms/FormModal.vue";
import ProjectCreateModal from "../components/ProjectCreateModal.vue";
import TemplateCreateModal from "../components/TemplateCreateModal.vue";
import {redirectTo} from "../../common/util";
import TemplatesTab from "../views/TemplatesTab.vue";
import ReportsTab from "../views/ReportsTab.vue";
import AddOnsTab from "../views/AddOnsTab.vue";
import TermsInfo from "../components/TermsInfo.vue";
import PendingRequisitionsNotice from "../components/PendingRequisitionsNotice.vue";
import VueI18n from "vue-i18n";
import Lockr from "lockr";
import Calendar from "../../common/Calendar.vue";
import HelpSidebar from "../../common/HelpSidebar.vue";
import SharedCandidatesTab from "../views/SharedCandidatesTab.vue";
import {LoadedPresentation} from "../util";
import TdDate from "../../common/library/TdDate.vue";
import DismissableAlert from "../../common/DismissableAlert.vue";

function isAnyCollapsedFilterActive(filters: ProjectFilters): boolean {
    return (
        !isEmpty(filters.clients) ||
        !isEmpty(filters.project_start_date_from) ||
        !isEmpty(filters.project_start_date_until) ||
        !isEmpty(filters.project_end_date_from) ||
        !isEmpty(filters.project_end_date_until) ||
        !isEmpty(filters.locations) ||
        Object.values(filters.custom_fields ?? {}).some(val => !isEmpty(val))
    );
}

Vue.use(TooltipPlugin);

export default defineComponent({
    components: {
        TdDate,
        HelpSidebar,
        Calendar,
        PendingRequisitionsNotice,
        TermsInfo,
        AddOnsTab,
        ReportsTab,
        TemplatesTab,
        SharedCandidatesTab,
        TemplateCreateModal,
        ProjectCreateModal,
        FormModal,
        ProjectsTab,
        StyledCheckbox,
        CheckboxDropdown,
        MainLayout,
        DashboardIndicator,
        BButton,
        BInputGroupText,
        BDropdown,
        BDropdownDivider,
        BDropdownItem,
        BOverlay,
        BNav,
        BNavItem,
        BFormInput,
        BInputGroup,
        BCollapse,
        BAlert,
        DismissableAlert,
    },
    props: {
        projects: {
            type: Object as PropType<PaginatedResults<ProjectWithRequisition>>,
            required: true,
        },
        teamsOptions: {
            type: Array as PropType<BSelectOption[]>,
            required: true,
        },
        filters: {
            type: Object as PropType<ProjectFilters>,
            required: true,
        },
        statusOptions: {
            type: Array as PropType<BSelectOption[]>,
            required: true,
        },
        rawUsersOptions: {
            type: Array as PropType<(BSelectOption & {active: boolean; role: UserRole})[]>,
            required: true,
        },
        orderByOptions: {
            type: Array as PropType<BSelectOption[]>,
            required: true,
        },
        clientsOptions: {
            type: Array as PropType<BSelectOption[]>,
            required: true,
        },
        locationOptions: {
            type: Array as PropType<BSelectOption[]>,
            required: true,
        },
        customFields: {
            type: Array as PropType<LFField[]>,
            required: true,
        },
        tab: {
            type: String as PropType<"projects" | "templates" | "reports" | "add-ons" | "shared-candidates">,
            required: true,
        },
        stageSummaries: {
            type: Array as PropType<StageSummary[]>,
            required: true,
        },
        templates: {
            type: Array as PropType<Project[]>,
            required: true,
        },
        termsInfo: {
            type: Object as PropType<TermsInfo>,
            required: true,
        },
        pendingRequisitionApprovals: {
            type: Array as PropType<RequisitionApproval[]>,
            required: true,
        },
    },
    data() {
        return {
            statsLoading: false,
            stats: {
                project_count: 0,
                candidate_count: 0,
                avg_candidate_count: 0,
                avg_duration: 0,
            },
            showFilters: isAnyCollapsedFilterActive(this.filters),
            showRequisitionForm: false,
            showActivitiesReportForm: false,
            showMonthlyHiringReportForm: false,
            showProjectCreateModal: false,
            selectedTemplateId: 0,
            showTemplateCreateModal: false,
            showInactiveUsers: false,
            presentations: null as null | LoadedPresentation[],
        };
    },
    created() {
        this.fetchStats();
        this.showInactiveUsers = Lockr.get("project_show_inactive_users", false);
        this.fetchPresentations();
    },
    computed: {
        isAnyCollapsedFilterActive(): boolean {
            return isAnyCollapsedFilterActive(this.filters);
        },
        usersOptions(): BMultiSelectOption[] {
            let users;
            if (this.showInactiveUsers) {
                users = this.rawUsersOptions;
            } else {
                users = this.rawUsersOptions.filter(user => user.active);
            }

            return sortBy(users, ["name"]).map(user => {
                return {
                    value: user.id!,
                    label: user.name + (user.active ? "" : " (inactive)"),
                };
            });
        },
        disableToggleInactiveUsers(): boolean {
            const inactiveUserIds = this.rawUsersOptions.filter(user => !user.active).map(user => user.id);
            return this.filters.users.some((userId: number) => inactiveUserIds.includes(userId));
        },
        toggleInactiveUsersTooltip(): VueI18n.TranslateResult {
            if (this.disableToggleInactiveUsers) {
                return this.$t("Some inactive users are selected. Deselect them to hide inactive users.");
            }
            return this.showInactiveUsers ? this.$t("Hide inactive users") : this.$t("Show inactive users");
        },
        filtersButtonTooltip(): unknown {
            return {
                title: this.$t("You have active filters. Clear filters to hide this.").toString(),
                placement: "left",
                trigger: "hover",
                disabled: !this.isAnyCollapsedFilterActive || !this.showFilters,
            };
        },
    },
    methods: {
        redirectTo,
        getResults(page: number = 1): void {
            const query = page === 1 ? "" : `?page=${page}`;
            this.$inertia.visit(`/projects` + query, {
                method: "post",
                data: {
                    page,
                    filters: this.filters,
                },
                preserveState: true,
                only: ["projects", "stageSummaries"],
            });
            this.fetchStats();
        },
        fetchStats(noFilters = false) {
            this.statsLoading = true;
            axios
                .post(`/projects/stats`, {
                    filters: noFilters ? {} : this.filters,
                })
                .then((r: AxiosResponse<ProjectStats>) => {
                    this.stats = r.data;
                    this.statsLoading = false;
                });
        },
        fetchPresentations(): void {
            axios.get("/presentations").then((response: AxiosResponse<LoadedPresentation[]>) => {
                this.presentations = response.data;
            });
        },
        clearFilters() {
            const newFilters: ProjectFilters = {
                users: [],
                clients: [],
                users_only_managers: false,
                statuses: [],
                order_by: [],
                show_only_my_projects: false,
                exclude_continuous: false,
                custom_fields: zipObject(
                    Object.keys(this.filters.custom_fields),
                    Array(Object.keys(this.filters.custom_fields).length).fill([])
                ),
                name_query: null,
                project_end_date_from: null,
                project_end_date_until: null,
                project_start_date_from: null,
                project_start_date_until: null,
                locations: [],
                duration: {
                    period: [],
                    period_type: "last_12_months",
                    group_by: "month",
                },
                team_ids: [],
                teams_with_descendants: false,
            };
            for (const f in newFilters) {
                // @ts-ignore
                this.filters[f] = newFilters[f];
            }
        },
        toggleInactiveUsers(): void {
            if (this.disableToggleInactiveUsers) {
                return;
            }

            this.showInactiveUsers = !this.showInactiveUsers;
        },
        async triggerTotalReport(): void {
            const {data} = await axios.get(this.route("projects.totalReport"));

            this.$bvToast.toast(data.success, {
                title: data.title,
                variant: "success",
                solid: true,
            });
        },
        async createProjectFromTemplate(template: Project) {
            this.selectedTemplateId = template.id!;
            this.showProjectCreateModal = true;
        },
        showCNpsHelp() {
            this.$refs.help.show();
            window.analytics.track("Opened cNPS help", {trigger: "stat_overview_tooltip_click"});
        },
    },
    watch: {
        filters: {
            handler(): void {
                this.getResults(1);
            },
            deep: true,
        },
        showProjectCreateModal(v) {
            if (!v) {
                this.selectedTemplateId = 0;
            }
        },
    },
});
</script>

<template>
    <main-layout>
        <template #topSection>
            <div class="top-stats border-bottom">
                <div class="container">
                    <div class="row">
                        <div class="col">
                            <div
                                class="d-flex top-summary-stats-and-filters align-items-center flex-wrap gap-3 justify-content-between"
                            >
                                <b-overlay
                                    :show="statsLoading"
                                    rounded="sm"
                                    v-if="projects.data.length > 0"
                                >
                                    <template v-slot:overlay>&nbsp;</template>
                                    <div class="d-flex stat-box">
                                        <div class="stat-box__item">
                                            <div class="stat-box__label">
                                                {{ $t("Total projects") }}
                                            </div>
                                            <div class="stat-box__value">
                                                {{ stats.project_count }}
                                            </div>
                                        </div>
                                        <div class="stat-box__item">
                                            <div class="stat-box__label">
                                                {{ $t("Total candidates") }}
                                            </div>
                                            <div class="stat-box__value">
                                                {{ stats.candidate_count }}
                                            </div>
                                        </div>
                                        <div class="stat-box__item">
                                            <div class="stat-box__label">
                                                {{ $t("Avg. candidates") }}
                                            </div>
                                            <div class="stat-box__value">
                                                {{ stats.avg_candidate_count }}
                                            </div>
                                        </div>
                                        <dashboard-indicator
                                            :title="$t('Time to fill')"
                                            :indicator="stats.time_to_fill"
                                        ></dashboard-indicator>
                                        <dashboard-indicator
                                            :title="$t('Time to hire')"
                                            :indicator="stats.time_to_hire"
                                        ></dashboard-indicator>
                                        <dashboard-indicator
                                            :title="$t('cNPS')"
                                            :indicator="stats.cnps"
                                        >
                                            <template #after-title>
                                                <i
                                                    class="tdi td-tooltip-circle clickable ml-1"
                                                    @click="showCNpsHelp()"
                                                    v-b-tooltip.bottom="$t('Click here to read more about cNPS')"
                                                ></i>
                                            </template>
                                        </dashboard-indicator>
                                    </div>
                                </b-overlay>
                                <div class="d-flex align-items-center ml-auto">
                                    <div class="d-flex flex-column">
                                        <styled-checkbox v-model="filters.show_only_my_projects">
                                            {{ $t("Only my projects") }}
                                        </styled-checkbox>
                                    </div>
                                    <checkbox-dropdown
                                        variant="transparent"
                                        size=""
                                        toggle-class="font-weight-normal"
                                        v-model="filters.statuses"
                                        :options="statusOptions"
                                        :text="$t('Status')"
                                    >
                                        <template #after>
                                            <hr class="mx-n2 my-1" />
                                            <div class="dropdown-item">
                                                <styled-checkbox v-model="filters.exclude_continuous">
                                                    {{ $t("Exclude continuous projects") }}
                                                </styled-checkbox>
                                            </div>
                                        </template>
                                        <template #after-label>
                                            <template v-if="filters.exclude_continuous">*</template>
                                        </template>
                                    </checkbox-dropdown>
                                    <checkbox-dropdown
                                        v-if="
                                            $rlSettings.user.role !== 'limited' ||
                                            $rlSettings.organization_type === 'corporation'
                                        "
                                        variant="transparent"
                                        size=""
                                        toggle-class="font-weight-normal"
                                        v-model="filters.users"
                                        :options="usersOptions"
                                        :search="true"
                                        :text="$t('Users')"
                                        icon="td-users-two"
                                    >
                                        <div slot="top">
                                            <div class="dropdown-item">
                                                <styled-checkbox
                                                    no-wrap
                                                    v-model="filters.users_only_managers"
                                                >
                                                    {{ $t("Only project managers") }}
                                                </styled-checkbox>
                                            </div>
                                            <hr class="mt-2 mx-n2 mb-2" />
                                        </div>
                                        <template #extra>
                                            <span
                                                class="ml-auto"
                                                v-b-tooltip.bottom.hover="toggleInactiveUsersTooltip"
                                            >
                                                <b-button
                                                    variant="white"
                                                    size="xs"
                                                    class="align-items-center gap-1"
                                                    @click="toggleInactiveUsers"
                                                    :disabled="disableToggleInactiveUsers"
                                                >
                                                    <i
                                                        class="tdi td-sm"
                                                        :class="{
                                                            'td-eye': !showInactiveUsers,
                                                            'td-eye-off': showInactiveUsers,
                                                        }"
                                                    ></i>
                                                    <i class="tdi td-profile text-muted td-sm"></i>
                                                </b-button>
                                            </span>
                                        </template>
                                    </checkbox-dropdown>
                                    <checkbox-dropdown
                                        v-if="teamsOptions.length > 0"
                                        v-model="filters.team_ids"
                                        :options="teamsOptions"
                                        :search="true"
                                        :text="$t('Teams')"
                                        icon="td-users-group"
                                        size=""
                                        variant="transparent"
                                        toggle-class="font-weight-normal"
                                    >
                                        <div slot="top">
                                            <div class="dropdown-item">
                                                <styled-checkbox
                                                    no-wrap
                                                    v-model="filters.teams_with_descendants"
                                                >
                                                    {{ $t("With descendant teams") }}
                                                </styled-checkbox>
                                            </div>
                                            <hr class="mt-2 mx-n2 mb-2" />
                                        </div>
                                    </checkbox-dropdown>
                                    <template v-if="$rlSettings.user.role !== 'limited'">
                                        <b-dropdown
                                            right
                                            toggle-class="btn-sm btn-dark"
                                            no-caret
                                        >
                                            <template v-slot:button-content>
                                                <i class="tdi td-plus mr-2"></i>
                                                {{ $t("Create") }}
                                            </template>
                                            <b-dropdown-item
                                                @click="showProjectCreateModal = true"
                                                v-can.projects.create
                                            >
                                                {{ $t("Project") }}
                                            </b-dropdown-item>
                                            <b-dropdown-item
                                                @click="showTemplateCreateModal = true"
                                                v-if="$rlSettings.user.role === 'admin'"
                                                v-can.projects.create
                                            >
                                                {{ $t("Template") }}
                                            </b-dropdown-item>
                                            <b-dropdown-item
                                                :href="route('landings.v2.create')"
                                                v-can.landings.create
                                            >
                                                {{ $t("Landing page") }}
                                            </b-dropdown-item>
                                            <b-dropdown-item
                                                v-can.requisitions.create
                                                @click="showRequisitionForm = true"
                                            >
                                                {{ $t("Job requisition") }}
                                            </b-dropdown-item>
                                        </b-dropdown>
                                    </template>
                                    <template v-else>
                                        <button
                                            class="btn btn-primary btn-sm"
                                            v-can.requisitions.create
                                            @click="showRequisitionForm = true"
                                        >
                                            <i class="tdi td-plus mr-2"></i>
                                            {{ $t("Create requisition") }}
                                        </button>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="section-nav border-bottom">
                <div class="container d-flex align-items-center">
                    <b-nav class="nav-gap-md nav-bordered">
                        <b-nav-item
                            :active="tab === 'projects'"
                            @click="$inertia.visit(route('projects.index'), {only: ['tab'], preserveState: true})"
                            >{{ $t("Projects") }}
                        </b-nav-item>
                        <b-nav-item
                            :active="tab === 'templates'"
                            @click="$inertia.visit(route('projects.templates'), {only: ['tab'], preserveState: true})"
                            v-if="$rlSettings.user.role !== 'limited'"
                            >{{ $t("Templates") }}
                        </b-nav-item>
                        <b-nav-item
                            @click="$inertia.visit(route('projects.reports'), {only: ['tab'], preserveState: true})"
                            :active="tab === 'reports'"
                            v-if="$rlSettings.user.role !== 'limited'"
                        >
                            {{ $t("Recruitment performance") }}
                        </b-nav-item>
                        <b-nav-item
                            @click="$inertia.visit(route('projects.add-ons'), {only: ['tab'], preserveState: true})"
                            :active="tab === 'add-ons'"
                            v-if="$rlSettings.user.role === 'admin'"
                        >
                            {{ $t("Add-ons") }}
                        </b-nav-item>
                        <b-nav-item
                            @click="
                                $inertia.visit(route('projects.shared-candidates'), {
                                    only: ['tab'],
                                    preserveState: true,
                                })
                            "
                            :active="tab === 'shared-candidates'"
                        >
                            {{ $t("Shared candidates") }}
                        </b-nav-item>
                    </b-nav>
                    <div class="ml-auto d-flex align-items-center gap-2">
                        <b-input-group size="sm">
                            <template #prepend>
                                <b-input-group-text>
                                    <i class="tdi td-search td-sm"></i>
                                </b-input-group-text>
                            </template>
                            <b-form-input
                                class="font-weight-medium"
                                :placeholder="$t('Search')"
                                v-model="filters.name_query"
                                debounce="250"
                            ></b-form-input>
                            <template #append>
                                <b-button
                                    class="px-2"
                                    variant="white"
                                    size="sm"
                                    @click="filters.name_query = ''"
                                    v-b-tooltip.bottom.hover="$t('Clear search')"
                                >
                                    <i class="tdi td-close td-sm"></i>
                                </b-button>
                            </template>
                        </b-input-group>
                        <b-dropdown
                            toggle-class="btn-sm btn-white"
                            no-caret
                        >
                            <template v-slot:button-content>
                                <i class="tdi td-sort-arrow-down text-sm mr-2"></i> {{ $t("Sort by") }}
                                <small class="ml-2">
                                    <i class="fas fa-chevron-down"></i>
                                </small>
                            </template>
                            <b-dropdown-item
                                v-for="option in orderByOptions"
                                :key="option.value"
                                @click="filters.order_by = [option.value]"
                                link-class="d-flex align-items-baseline"
                            >
                                {{ option.label }}
                                <i
                                    class="tdi td-check ml-auto"
                                    v-if="
                                        filters.order_by[0] === option.value ||
                                        (!filters.order_by[0] && option.value === 'default')
                                    "
                                ></i>
                            </b-dropdown-item>
                        </b-dropdown>
                        <b-dropdown
                            toggle-class="btn-sm btn-white"
                            no-caret
                        >
                            <template v-slot:button-content>
                                <i class="fas fa-file-excel text-sm mr-2"></i> {{ $t("Export") }}
                                <small class="ml-2">
                                    <i class="fas fa-chevron-down"></i>
                                </small>
                            </template>
                            <b-dropdown-item
                                @click="triggerTotalReport()"
                                target="_blank"
                            >
                                {{ $t("Download candidates report") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                :href="route('projects.projectStatusReport')"
                                target="_blank"
                            >
                                {{ $t("Download project status report") }}
                            </b-dropdown-item>
                            <b-dropdown-item @click="showActivitiesReportForm = true">
                                {{ $t("Download custom activity report") }}
                            </b-dropdown-item>
                            <b-dropdown-item @click="showMonthlyHiringReportForm = true">
                                {{ $t("Download monthly hiring report") }}
                            </b-dropdown-item>
                        </b-dropdown>
                        <div v-b-tooltip="filtersButtonTooltip">
                            <button
                                class="btn btn-sm d-flex"
                                :class="{'btn-white': !showFilters, 'btn-light': showFilters}"
                                @click="showFilters = !showFilters"
                                :disabled="isAnyCollapsedFilterActive && showFilters"
                            >
                                {{ $t("Filters") }}
                                <i
                                    class="tdi td-filter-alt w-fixed-1 ml-2"
                                    v-if="!showFilters"
                                ></i>
                                <i
                                    class="tdi td-close w-fixed-1 ml-2"
                                    v-else
                                ></i>
                            </button>
                        </div>
                    </div>
                </div>
                <b-collapse v-model="showFilters">
                    <div class="border-bottom py-25">
                        <div class="container d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center column-gap-4 row-gap-2 flex-wrap">
                                <checkbox-dropdown
                                    v-if="
                                        $rlSettings.user.role !== 'limited' &&
                                        $rlSettings.organization_type === 'agency'
                                    "
                                    v-model="filters.clients"
                                    search
                                    variant="transparent"
                                    toggle-class="font-weight-normal"
                                    size="plain"
                                    :options="clientsOptions"
                                    :text="$t('Client')"
                                >
                                </checkbox-dropdown>
                                <b-dropdown
                                    variant="transparent"
                                    size="plain"
                                    no-caret
                                    lazy
                                >
                                    <template v-slot:button-content>
                                        {{ $t("Project start date") }}
                                        <template
                                            v-if="filters.project_start_date_from || filters.project_start_date_until"
                                        >
                                            (
                                            <td-date
                                                v-if="filters.project_start_date_from"
                                                :date="filters.project_start_date_from"
                                            ></td-date>
                                            <span v-else>...</span>
                                            &nbsp;&ndash;&nbsp;
                                            <td-date
                                                v-if="filters.project_start_date_until"
                                                :date="filters.project_start_date_until"
                                            ></td-date>
                                            <span v-else>...</span>)
                                        </template>
                                        <small class="ml-2">
                                            <i class="fas fa-chevron-down"></i>
                                        </small>
                                    </template>
                                    <div class="p-2">
                                        <div class="d-flex">
                                            <div class="mr-1">
                                                <div class="d-flex mb-1">
                                                    <span>
                                                        {{ $t("From") }}
                                                        <td-date
                                                            v-if="filters.project_start_date_from"
                                                            :date="filters.project_start_date_from"
                                                        ></td-date>
                                                    </span>
                                                    <a
                                                        href="#"
                                                        v-if="filters.project_start_date_from"
                                                        class="btn btn-white btn-xs ml-2"
                                                        @click.prevent.stop="filters.project_start_date_from = null"
                                                    >
                                                        {{ $t("Clear") }}
                                                    </a>
                                                </div>
                                                <calendar
                                                    v-model="filters.project_start_date_from"
                                                    hide-header
                                                ></calendar>
                                            </div>
                                            <div class="ml-1">
                                                <div class="d-flex mb-1">
                                                    <span>
                                                        {{ $t("Until") }}
                                                        <td-date
                                                            v-if="filters.project_start_date_until"
                                                            :date="filters.project_start_date_until"
                                                        ></td-date>
                                                    </span>
                                                    <a
                                                        href="#"
                                                        v-if="filters.project_start_date_until"
                                                        class="btn btn-white btn-xs ml-2"
                                                        @click.prevent.stop="filters.project_start_date_until = null"
                                                    >
                                                        {{ $t("Clear") }}
                                                    </a>
                                                </div>
                                                <calendar
                                                    v-model="filters.project_start_date_until"
                                                    hide-header
                                                ></calendar>
                                            </div>
                                        </div>
                                    </div>
                                </b-dropdown>
                                <b-dropdown
                                    variant="transparent"
                                    size="plain"
                                    no-caret
                                    lazy
                                >
                                    <template v-slot:button-content>
                                        {{ $t("Project end date") }}
                                        <template
                                            v-if="filters.project_end_date_from || filters.project_end_date_until"
                                        >
                                            (
                                            <td-date
                                                v-if="filters.project_end_date_from"
                                                :date="filters.project_end_date_from"
                                            ></td-date>
                                            <span v-else>...</span>
                                            &nbsp;&ndash;&nbsp;
                                            <td-date
                                                v-if="filters.project_end_date_until"
                                                :date="filters.project_end_date_until"
                                            ></td-date>
                                            <span v-else>...</span>)
                                        </template>
                                        <small class="ml-2">
                                            <i class="fas fa-chevron-down"></i>
                                        </small>
                                    </template>
                                    <div v-if="false">
                                        <b-dropdown-item>{{ $t("MTD") }}</b-dropdown-item>
                                        <b-dropdown-item>{{ $t("Previous month") }}</b-dropdown-item>
                                        <b-dropdown-item>{{ $t("YTD") }}</b-dropdown-item>
                                        <b-dropdown-item>{{ $t("Last 30 days") }}</b-dropdown-item>
                                        <b-dropdown-item>{{ $t("Last 60 days") }}</b-dropdown-item>
                                        <b-dropdown-item>{{ $t("Last 90 days") }}</b-dropdown-item>
                                        <b-dropdown-item>{{ $t("Custom range") }}</b-dropdown-item>
                                    </div>

                                    <div class="p-2">
                                        <div class="d-flex">
                                            <div class="mr-1">
                                                <div class="d-flex mb-1">
                                                    <span>
                                                        {{ $t("From") }}
                                                        <td-date
                                                            v-if="filters.project_end_date_from"
                                                            :date="filters.project_end_date_from"
                                                        ></td-date>
                                                    </span>
                                                    <a
                                                        href="#"
                                                        v-if="filters.project_end_date_from"
                                                        class="btn btn-white btn-xs ml-2"
                                                        @click.prevent.stop="filters.project_end_date_from = null"
                                                    >
                                                        {{ $t("Clear") }}
                                                    </a>
                                                </div>
                                                <calendar
                                                    v-model="filters.project_end_date_from"
                                                    hide-header
                                                ></calendar>
                                            </div>
                                            <div class="ml-1">
                                                <div class="d-flex mb-1">
                                                    <span>
                                                        {{ $t("Until") }}
                                                        <td-date
                                                            v-if="filters.project_end_date_until"
                                                            :date="filters.project_end_date_until"
                                                        ></td-date>
                                                    </span>
                                                    <a
                                                        href="#"
                                                        v-if="filters.project_end_date_until"
                                                        class="btn btn-white btn-xs ml-2"
                                                        @click.prevent.stop="filters.project_end_date_until = null"
                                                    >
                                                        {{ $t("Clear") }}
                                                    </a>
                                                </div>
                                                <calendar
                                                    v-model="filters.project_end_date_until"
                                                    hide-header
                                                ></calendar>
                                            </div>
                                        </div>
                                    </div>
                                </b-dropdown>
                                <checkbox-dropdown
                                    variant="transparent"
                                    toggle-class="font-weight-normal"
                                    size="plain"
                                    v-model="filters.locations"
                                    :options="locationOptions"
                                    :text="$t('Location')"
                                >
                                </checkbox-dropdown>
                                <checkbox-dropdown
                                    variant="transparent"
                                    toggle-class="font-weight-normal"
                                    size="plain"
                                    v-model="filters.custom_fields[cf.key]"
                                    :options="cf.items"
                                    :text="cf.label"
                                    v-for="cf in customFields.filter(c => ['tags', 'select'].includes(c.type))"
                                    :key="cf.label"
                                >
                                </checkbox-dropdown>
                            </div>
                            <div class="ml-3">
                                <button
                                    class="btn btn-plain d-flex align-items-baseline"
                                    style="white-space: nowrap"
                                    @click="clearFilters"
                                >
                                    <i class="tdi td-close mr-2"></i>
                                    {{ $t("Clear filters") }}
                                </button>
                            </div>
                        </div>
                    </div>
                </b-collapse>
            </div>
        </template>
        <div class="container">
            <dismissable-alert
                id="event-2025-05-29-1"
                variant="success"
                icon="video"
                class="mt-3"
                v-if="false"
            >
                <b>Free workshop! </b>
                <b>Boost efficiency & compliance with AI-powered Credentials Validity & Reference Checks! </b>
                <b>Thurs 29th May - 1PM EST / 11AM GMT</b>
                <template #actions>
                    <a
                        href="https://app.livestorm.co/teamdash/master-compliance-with-ease"
                        target="_blank"
                        class="btn btn-white-success btn-sm"
                        >{{ $t("Register") }}</a
                    >
                </template>
            </dismissable-alert>
            <terms-info :terms-info="termsInfo"></terms-info>
            <pending-requisitions-notice :approvals="pendingRequisitionApprovals"></pending-requisitions-notice>
            <div>
                <projects-tab
                    v-if="tab === 'projects'"
                    :projects="projects"
                    @change="getResults"
                    @create-project="showProjectCreateModal = true"
                    @create-requisition="showRequisitionForm = true"
                    :stage-summaries="stageSummaries"
                ></projects-tab>
                <templates-tab
                    :templates="templates"
                    v-if="tab === 'templates'"
                    :projects="projects"
                    @create-template="showTemplateCreateModal = true"
                    @create-from-template="createProjectFromTemplate($event)"
                >
                </templates-tab>
                <reports-tab
                    :filters="filters"
                    v-if="tab === 'reports'"
                ></reports-tab>
                <add-ons-tab v-if="tab === 'add-ons'"></add-ons-tab>
                <shared-candidates-tab
                    v-if="tab === 'shared-candidates'"
                    :data="presentations ? {loaded: true, presentations} : {loaded: false}"
                />
            </div>
            <form-modal
                :title="$t('Create a new job requisition')"
                form-name="RequisitionForm"
                v-model="showRequisitionForm"
                :to-load="{files: [], recruiter_id: null}"
                :label-save="$t('Create requisition')"
                :label-success="$t('Job requisition added')"
                @response="redirectTo(`/requisitions/${$event.payload.updates.id}`)"
            ></form-modal>
            <form-modal
                form-name="ActivitiesReportForm"
                v-model="showActivitiesReportForm"
                size="md"
                :to-load="{}"
            ></form-modal>
            <form-modal
                :title="$t('Download monthly hiring report')"
                form-name="MonthlyHiringReportForm"
                v-model="showMonthlyHiringReportForm"
                :label-save="$t('Download')"
                :label-success="$t('Downloading report...')"
                size="md"
                :to-load="{}"
            >
                <template #after-form>
                    <div
                        v-if="stats.project_count"
                        class="text-center mt-3"
                    >
                        <p
                            class="mb-0"
                            v-text="
                                $tc(
                                    '{count} project will be included in the report.|{count} projects will be included in the report.',
                                    stats.project_count
                                )
                            "
                        />
                        <p
                            class="mb-0"
                            v-text="$t('You can modify the filters to change this.')"
                        />
                    </div>
                    <b-alert
                        v-else
                        show
                    >
                        {{
                            $t("No projects are included in this report. Change the filters to include some projects.")
                        }}
                    </b-alert>
                </template>
            </form-modal>
            <project-create-modal
                v-model="showProjectCreateModal"
                :initial-selected-template="selectedTemplateId"
                @project-created="redirectTo(`/projects/${$event.payload.updates.id}`)"
                @on-new-template="
                    showProjectCreateModal = false;
                    showTemplateCreateModal = true;
                "
            ></project-create-modal>
            <template-create-modal
                v-model="showTemplateCreateModal"
                @project-created="redirectTo(`/projects/${$event.payload.updates.id}`)"
            ></template-create-modal>
        </div>
        <help-sidebar ref="help"></help-sidebar>
    </main-layout>
</template>
