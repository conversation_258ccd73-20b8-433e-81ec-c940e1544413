// Custom
$ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
$ease-in-out-expo: cubic-bezier(0.87, 0, 0.13, 1);

// Colors
$white: #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #adb5bd !default;
$gray-600: #6c757d !default;
$gray-700: #495057 !default;
$gray-800: #343a40 !default;
$gray-900: #212529 !default;
$black: #000 !default;

// Green
$gumdrop-green-100: #e5f4f0;
$gumdrop-green-200: #BEE3DA;
$gumdrop-green-300: #7DC7B4;
$gumdrop-green-400: #37A98C;
$gumdrop-green-500: #27856D;
$gumdrop-green-600: #1D6351;
$gumdrop-green-700: #144337;
$gumdrop-green-800: #0B251E;

// Gold
$usc-gold-100: #fff1b9;
$usc-gold-200: #FFD93F;
$usc-gold-300: #E0B300;
$usc-gold-400: #B89300;
$usc-gold-500: #917400;
$usc-gold-600: #6B5600;
$usc-gold-700: #483A00;
$usc-gold-800: #282000;

// Violet
$ultraviolet-berl-100: #F9EDFA;
$ultraviolet-berl-200: #F3D7F4;
$ultraviolet-berl-300: #E6AEE8;
$ultraviolet-berl-400: #D983DC;
$ultraviolet-berl-500: #CB54CF;
$ultraviolet-berl-600: #A837AC;
$ultraviolet-berl-700: #7E2A81;
$ultraviolet-berl-800: #561D59;
$ultraviolet-berl-900: #311033;

// Red
$ken-masters-100: #FDEAE9;
$ken-masters-200: #F9C9C8;
$ken-masters-300: #F39492;
$ken-masters-400: #ED5E5B;
$ken-masters-500: #E72824;
$ken-masters-600: #AD1E1B;
$ken-masters-700: #741412;
$ken-masters-800: #3A0A09;

$white-transparent: rgba($white, 0.5);

$yellow: $usc-gold-300;
$green: $gumdrop-green-400;
$purple: $ultraviolet-berl-400;
$red: $ken-masters-400;
$dark: #000000;
$dark-medium: rgba($dark, 0.8);
$dark-light: rgba($dark, 0.7);

$shark-grey: #e4e9e7;
$shark-grey-medium: #eef1f0;
$shark-grey-light: #f7f8f8;
$shark-grey-lightest: #fbfbfb;
$shark-grey-dark: #dce2e0;

$light: $shark-grey;
$light-medium: $shark-grey-medium;
$lighter: $shark-grey-light;
$lightest: $shark-grey-lightest;
$primary: $dark;
$secondary: darken($shark-grey, 30%);

$content-wrapper-bg: $shark-grey-light;

$info: $usc-gold-100;

$colors: (
    "white-transparent": $white-transparent,
    "shark-grey": $shark-grey,
    "shark-grey-light": $shark-grey-light,
    "dark-light": $dark-light,
    "transparent": transparent,
);

$theme-colors: (
    "light-medium": $light-medium,
    "lighter": $lighter,
    "lightest": $lightest,
    "white-transparent": $white-transparent,
    "dark-light": $dark-light,
);

$theme-colors-light: (
    "success": $gumdrop-green-200,
    "warning": $usc-gold-100,
    "danger": $ken-masters-100,
);

$theme-colors-dark: (
    "success": $gumdrop-green-500,
    "warning": $usc-gold-500,
    "danger": $ken-masters-600,
);

// Borders
$border-color: rgba($dark, 0.1);
$border-radius-lg: 0.5rem;
$border-radius-xl: 0.75rem;
$border-radius-xxl: 1rem;

// Fonts
$font-family-base: 'Geist', sans-serif;
$font-weight-lighter: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 600;
$font-size-base: 1rem;
$font-size-xxl: $font-size-base * 1.5 !default;
$font-size-xl: $font-size-base * 1.25 !default;
$font-size-sm: 0.875rem;
$font-size-xs: 0.8125rem;
$font-size-xxs: 0.75rem;
$font-size-tiny: 0.7rem;

// Headings
$headings-color: $dark;
$headings-font-weight: $font-weight-bold;
$h1-font-size: $font-size-base * 1.5 !default;
$h2-font-size: $font-size-base * 1.25 !default;
$h3-font-size: $font-size-base * 1 !default;
$h4-font-size: $font-size-base * 0.875 !default;
$h5-font-size: $font-size-base * 0.75 !default;
$h6-font-size: $font-size-base * 0.5 !default;

// Text
$body-color: $dark-light;
$text-muted: $secondary;

$kbd-color: $body-color !default;
$kbd-bg: $shark-grey !default;

// Buttons
$btn-border-radius: 9999px;
$btn-border-radius-lg: 9999px;
$btn-border-radius-sm: 9999px;
$btn-border-radius-xs: 9999px;
$btn-font-weight: 500;
$btn-circle-size: 2rem;

$input-btn-padding-x: 0.75rem;
$input-btn-padding-y: 0.375rem;

$input-btn-padding-x-sm: 0.75rem;
$input-btn-padding-y-sm: 0.5rem;

$input-btn-padding-x-lg: 1rem;
$input-btn-padding-y-lg: 1rem;

$input-btn-font-size: $font-size-sm;
$input-btn-font-size-sm: $font-size-xs;
$input-btn-line-height-sm: 1;
//$input-btn-line-height-sm: 0.8125;

$btn-padding-y-xs: 0.25rem;
$btn-padding-x-xs: 0.75rem;
$btn-font-size-xs: $font-size-xxs;
$btn-line-height-xs: 1;

// Inputs
$input-border-radius: 9999px;
$input-border-radius-lg: 9999px;
$input-border-radius-sm: 9999px;

$input-border-color: $shark-grey;

$input-color: $dark;

//$input-line-height: 1;
$input-line-height-sm: 1;

$input-padding-y: 0.5rem;
$input-padding-x: $input-btn-padding-x;

$input-group-addon-bg: $white;

$custom-select-border-radius: $input-border-radius;
$custom-select-font-weight: $font-weight-medium;
$custom-select-color: $gray-900;
$custom-select-line-height: 1rem;
$custom-select-padding-y: $input-padding-y;
$custom-select-padding-x: $input-padding-x;
$custom-select-bg-size: 10px 5px;
// #999 matches the multiselect element's dropdown arrow color
$custom-select-indicator: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='10' height='5' viewBox='0 0 10 5'%3e%3cpolygon points='0,0 10,0 5,5' style='fill:%23999'%3e%3c/polygon%3e%3c/svg%3e");
$custom-select-background: $custom-select-indicator right 0.92rem center / $custom-select-bg-size no-repeat;

// Navbar
$navbar-padding-y: 0;
$navbar-padding-x: 0;
$navbar-nav-link-padding-x: 0;
$nav-link-padding-x: 0;
$nav-link-padding-y: 1rem;
$nav-link-disabled-color: $gray-500;

// Links
$link-color: $dark;

// Containers
$container-max-widths: (
    sm: 540px,
    md: 720px,
    lg: 960px,
    xl: 1312px
);

// Spacing
$spacers: (
    xxs: 0.125rem,
    25: 0.75rem,
    5: 2rem,
    6: 3rem,
);

// Cards
$card-border-radius: $border-radius-xl;
$card-border-color: $shark-grey;
$card-cap-bg: $white;
$card-spacer-x: 1.5rem;
$card-spacer-y: 1rem;
$card-spacer-x-sm: 1rem;
$card-spacer-y-sm: 1rem;

// Badges
$badge-padding-y: 0.25rem;
$badge-pill-padding-x: 0.75rem;
$badge-font-size: 100%;
$badge-font-weight: $font-weight-normal;

// Pagination
$pagination-active-border-color: $gray-300;
$pagination-active-bg: $light-medium;
$pagination-active-color: $dark;
$pagination-hover-bg: $lighter;
$pagination-bg: transparent;

// Shadows:
$box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08), 0px 0px 6px 0px rgba(0, 0, 0, 0.02);
$box-shadow-lg: 0px 4px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 12px 0px rgba(0, 0, 0, 0.04);

// Dropdowns
$dropdown-font-size: $font-size-sm;
$dropdown-border-radius: $border-radius-xl;
$dropdown-border-color: $shark-grey;
$dropdown-padding-x: 0.5rem;
$dropdown-padding-y: 0.5rem;
$dropdown-item-padding-x: 0.5rem;
$dropdown-item-padding-y: 0.25rem;
$dropdown-header-padding: 0;

// Modals
$modal-content-border-radius: $border-radius-xl;
$modal-header-border-width: 0;
$modal-footer-border-width: 1px;
$modal-header-padding-x: 1.5rem;
$modal-inner-padding: 1rem 1.5rem;
$modal-lg: 60vw;
$modal-xl: 70vw;

// Alerts
$alert-border-radius: $border-radius-lg;

// Tooltips
$tooltip-border-radius: $border-radius-lg;

// Popovers
$popover-border-color: $border-color;
$popover-border-radius: $border-radius-lg;

// Toasts
$toast-border-radius: $border-radius-lg;
$toast-header-background-color: transparent;
$toast-header-border-color: transparent;
$toast-padding-y: 0.5rem;
$b-toast-background-opacity: 0.95;

$component-active-bg: $shark-grey-dark;
$component-active-color: $dark;

// Laraform
$lf-primary-color: $dark;

// Grid
$grid-breakpoints: (
    xs: 0,
    sm: 576px,
    md: 768px,
    lg: 992px,
    xl: 1200px,
    xxl: 1601px,
) !default;
