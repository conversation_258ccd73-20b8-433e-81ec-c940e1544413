import {test, expect} from "./fixtures/fresh-home-page";

test("main project roles functionality works", async ({freshHomePage}) => {
    const page = freshHomePage;

    // Create new role
    await page.getByRole("link", {name: ""}).click();
    // wait for the page to load
    await page.waitForURL(/organization\/settings/);

    await page.getByRole("link", {name: " Project-based roles"}).click();
    await page.getByRole("link", {name: " Add project role"}).first().click();
    await page.getByRole("textbox", {name: "Name"}).click();
    await page.getByRole("textbox", {name: "Name"}).fill("New user role");
    await page.getByRole("button", {name: "Save"}).click();
    await expect(page.locator("tbody")).toContainText("New user role");

    // Add role to user then remove it
    await page.getByRole("link", {name: "Projects"}).click();
    await page.getByRole("link", {name: "Your first project"}).click();
    await page.getByRole("button", {name: ""}).first().click();
    await page.locator('[id="project-users-modal"]').waitFor({state: "visible"});
    await page.waitForTimeout(500);
    await page.getByRole("button", {name: "project-based roles"}).click();
    await page.locator("label").filter({hasText: "New user role"}).locator("div").click();
    await page.getByRole("button", {name: "project-based roles"}).click();

    await expect(page.locator('[id="project-users-modal"]')).toContainText("New user role");
    await page.locator(".d-flex > .badge > .tdi").click();
    await expect(page.locator('[id="project-users-modal"]')).not.toContainText("New user role");
    await page.locator('[id="project-users-modal"]').getByRole("button", {name: ""}).click();
});
