import {Page, test as base, expect} from "@playwright/test";
// @ts-ignore
import {execSync} from "node:child_process";

type MyFixtures = {
    freshHomePage: Page;
};

export const test = base.extend<MyFixtures>({
    freshHomePage: async ({page}, use) => {
        // Set up the fixture.
        const e2eRefSlug = process.env.E2E_REF_SLUG;
        const artisanDirectory = process.env.ARTISAN_DIRECTORY ?? "";
        const websiteId = execSync(`php ${artisanDirectory}artisan debug:find-website-id ${e2eRefSlug}`)
            .toString()
            .trim();

        if (!websiteId) {
            test.fail(true, "Website not found");
        }

        execSync(`php ${artisanDirectory}artisan instance:reset --website_id=${websiteId}`);
        execSync(`php ${artisanDirectory}artisan cache:clear`);

        await page.goto("/register");

        await page.getByPlaceholder("Full name").fill("Test Person");
        // Email
        await page.getByPlaceholder("Email").fill("<EMAIL>");

        // Password
        await page.getByPlaceholder("Password", {exact: true}).fill("passwordpassword123");
        await page.getByPlaceholder("Retype password", {exact: true}).fill("passwordpassword123");

        // Organization name
        await page.getByPlaceholder("Organization name").fill("Test Organization");

        // phone
        await page.getByPlaceholder("Phone").fill("+372 1234 1234");

        // check tos
        await page.getByRole("checkbox", {name: "I accept the terms of use"}).check();
        await page.getByRole("checkbox", {name: "I accept the privacy policy"}).check();

        // click register button
        await page.getByRole("button", {name: "Register"}).click();

        await page.waitForURL("/projects");

        await expect(page.getByText("Timezone mismatch")).toBeVisible();

        await page.getByRole("button", {name: /Keep current timezone/i}).click();

        await expect(page.getByText("Timezone mismatch")).toBeHidden();

        await expect(page.getByText("Your first project")).toBeVisible();

        // Use the fixture value in the test.
        await use(page);

        // Log out
        await page.goto("/projects");
        await page.getByRole("link", {name: "Test Person"}).click();
        await page.getByRole("button", {name: "Log out"}).click();
        await page.locator("body").click();
    },
});

export {expect} from "@playwright/test";
