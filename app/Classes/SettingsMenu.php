<?php

namespace App\Classes;

use App\Models\Setting;

class SettingsMenu
{
    public static function items()
    {
        return [
            'candidates' => [
                'label' => __('Candidate management'),
                'items' => [
                    [
                        'label' => __('Job ads'),
                        'route' => 'landings.index',
                        'icon' => 'td-share-alt',
                        'permission' => 'landings.view',
                        'matchRouteIs' => 'landings.*',
                        'inertia' => true,
                    ],
                    [
                        'label' => __('Forms'),
                        'route' => 'forms.index',
                        'icon' => 'td-paper',
                        'permission' => 'forms.view',
                        'matchRouteIs' => 'forms.*',
                        'inertia' => true,
                    ],
                    [
                        'label' => __('Job requisitions'),
                        'route' => 'requisitions.index',
                        'icon' => 'td-cases',
                        'permission' => 'requisitions.view',
                        'matchRouteIs' => 'requisitions.*',
                    ],
                    [
                        'label' => __('Scorecards'),
                        'route' => 'scorecards.index',
                        'icon' => 'td-report',
                        'permission' => 'scorecards.view',
                        'matchRouteIs' => 'scorecards.*',
                    ],
                    [
                        'label' => __('References'),
                        'route' => 'references.index',
                        'icon' => 'td-check-double',
                        'permission' => 'referenceForms.view',
                        'matchRouteIs' => 'references.*',
                    ],
                    [
                        'label' => __('Custom activities'),
                        'route' => 'activityTypes.index',
                        'icon' => 'td-activity',
                        'permission' => 'activityTypes.view',
                        'matchRouteIs' => 'activityTypes.*',
                    ],
                    [
                        'label' => __('Custom stage categories'),
                        'route' => 'customStageCategory.edit',
                        'icon' => 'td-apps',
                        'setting' => \App\Models\Setting::KEY_USE_CUSTOM_STAGE_CATEGORIES,
                        'permission' => 'projectTemplates.create',
                        'matchRouteIs' => 'customStageCategory.*',
                    ],
                    [
                        'label' => __('Dropout reasons'),
                        'route' => 'dropoutReasons.index',
                        'icon' => 'td-unlike',
                        'permission' => 'dropoutReasons.view',
                        'matchRouteIs' => 'dropoutReasons.*',
                        'inertia' => true,
                    ],
                    [
                        'label' => __('Project failure reasons'),
                        'route' => 'projectFailureReasons.index',
                        'icon' => 'fa-times',
                        'permission' => 'projectFailureReasons.view',
                        'matchRouteIs' => 'projectFailureReasons.*',
                    ],
                    [
                        'label' => __('Tags'),
                        'route' => 'settings.tags.candidates.index',
                        'icon' => 'td-tag',
                        'permission' => 'tags.view',
                        'matchRouteIs' => 'settings.tags.*',
                    ],
                    [
                        'label' => __('Video interviews'),
                        'route' => 'video-interviews.index',
                        'icon' => 'td-video',
                        'permission' => 'videoInterviews.view',
                        'matchRouteIs' => 'video-interviews.*',
                        'inertia' => true,
                    ],
                    [
                        'label' => __('Message templates'),
                        'route' => 'templates.index',
                        'icon' => 'td-message',
                        'permission' => 'templates.view',
                        'matchRouteIs' => 'templates.*',
                        'inertia' => true,
                    ],
                    ...(Setting::get(Setting::KEY_USE_PROJECT_LOCATIONS_FEATURE) ? [
                        [
                            'label' => __('Locations'),
                            'route' => 'locations.index',
                            'icon' => 'td-pin-map',
                            'permission' => 'locations.view',
                            'matchRouteIs' => 'locations.*',
                        ],
                    ] : []),
                    [
                        'label' => __('File types'),
                        'route' => 'settings.file-types.index',
                        'icon' => 'td-attach',
                        'permission' => 'fileTypes.view',
                        'matchRouteIs' => 'settings.file-types.*',
                        'inertia' => true,
                    ],
                    [
                        'label' => __('Consent subtypes'),
                        'route' => 'consent-subtypes.index',
                        'icon' => 'td-check',
                        'permission' => 'consentSubtypes.update',
                        'matchRouteIs' => 'consent-subtypes.*',
                        'inertia' => true,
                    ],
                ],
            ],
            'accounts' => [
                'label' => __('Users & accounts'),
                'items' => [
                    [
                        'label' => __('My account'),
                        'route' => 'user.me',
                        'icon' => 'td-user',
                        'inertia' => true,
                        'roles' => ['admin', 'user'],
                        'matchFullUrlIs' => auth()->user() ? route('users.edit', auth()->user()) : null,
                    ],
                    [
                        'label' => __('Users'),
                        'route' => 'users.index',
                        'icon' => 'td-users-two',
                        'permission' => 'organization.update',
                        'matchRouteIs' => 'users.*',
                        'matchFullUrlIsNot' => auth()->user() ? route('users.edit', auth()->user()) : null,
                        'inertia' => true,
                    ],
                    [
                        'label' => __('Teams'),
                        'route' => 'teams.index',
                        'icon' => 'td-users-group',
                        'permission' => 'teams.create',
                        'matchRouteIs' => 'teams.*',
                    ],
                    [
                        'label' => __('SCIM group mappings'),
                        'route' => 'settings.scim.index',
                        'icon' => 'td-folder',
                        'permission' => 'scim.update',
                        'matchRouteIs' => 'settings.scim.*',
                    ],
                    [
                        'label' => __('Project-based roles'),
                        'route' => 'settings.project-roles.index',
                        'icon' => 'td-add-user',
                        'permission' => 'projectRoles.view',
                        'matchRouteIs' => 'settings.project-roles.*',
                        'inertia' => true,
                    ],
                ],
            ],
            'admin' => [
                'label' => __('Administration'),
                'items' => [
                    [
                        'label' => __('Integrations'),
                        'route' => 'integrations.index',
                        'icon' => 'td-scale',
                        'permission' => 'integrations.view',
                        'matchRouteIs' => 'integrations.*',
                    ],
                    [
                        'label' => __('Organization settings'),
                        'route' => 'organization.settings',
                        'icon' => 'td-settings',
                        'permission' => 'organization.update',
                        'matchRouteIs' => 'organization.*',
                        'inertia' => true,
                    ],
                    [
                        'label' => __('Audit log'),
                        'route' => 'audits.index',
                        'icon' => 'fa-history',
                        'permission' => 'audits.view',
                        'matchRouteIs' => 'audits.*',
                    ],
                    [
                        'label' => __('Mail identities'),
                        'route' => 'mailIdentities.index',
                        'icon' => 'td-mail',
                        'permission' => 'mailIdentities.view',
                        'matchRouteIs' => 'mailIdentities.*',
                    ],
                    [
                        'label' => __('API'),
                        'route' => 'settings.api.keys.index',
                        'icon' => 'td-code',
                        'permission' => 'api.view',
                        'matchRouteIs' => 'settings.api.*',
                        'inertia' => true,
                    ],
                    [
                        'label' => __('Authentication providers'),
                        'route' => 'authconfigs.index',
                        'icon' => 'td-lock',
                        'permission' => 'authConfigs.view',
                        'matchRouteIs' => 'authconfigs.*',
                    ],
                    [
                        'label' => __('Appearance'),
                        'route' => 'settings.appearance.index',
                        'icon' => 'td-light-mode',
                        'permission' => 'fonts.view',
                        'matchRouteIs' => 'settings.fonts.*',
                    ],
                ],
            ],
        ];
    }
}
