<?php

namespace App\DataExchange\StructuredJobs\Outputs;

use App\DataExchange\StructuredJobs\BaseOutput;
use App\DataExchange\StructuredJobs\Interfaces\ControlsFields;
use App\DataExchange\StructuredJobs\Interfaces\PassiveOutput;
use App\DataExchange\StructuredJobs\Interfaces\PassiveSingleAdOutput;
use App\Models\File;
use App\Models\Integration;
use App\Models\Setting;
use App\Models\StructuredJobAd;

class DefaultApiOutput extends BaseOutput implements ControlsFields, PassiveOutput, PassiveSingleAdOutput
{
    private $perPage = 15;

    public function getOutput()
    {
        if ($this->integration->remote_type !== Integration::TYPE_DEFAULT_FEED) {
            return response()->json([
                'message' => 'Invalid feed output type.',
                'code' => 'INCOMPATIBLE_OUTPUT_TYPE',
            ], 400);
        }
        $perPage = (int) request('per_page', $this->perPage);
        if ($perPage > 100) {
            $perPage = 100;
        }

        $sortBy = request('sort_by', 'id');
        $sortOrder = request('sort_order', 'asc');

        $keepVisible = $this->getKeepVisible();

        $jobAdsQuery = $this->integration->structuredJobAds()
            ->orderBy($sortBy, $sortOrder)
            ->with(['landing', 'image', 'project.team', 'emotionimages']);

        if (request()->get('with_archived') !== 'true') {
            $jobAdsQuery->active();
        }

        $res = $jobAdsQuery
            ->paginate($perPage);

        collect($res->items())->each(function (StructuredJobAd $ad) use ($keepVisible) {
            $this->setFieldsOnSingleAd($ad, $keepVisible);
        });

        return $res;
    }

    public static function getRequiredFields(): array
    {
        return [];
    }

    public static function getAcceptedFields(): array
    {
        $fields = [
        ];
        if (Setting::get(Setting::KEY_ORGANIZATION_TYPE) === Setting::ORGANIZATION_TYPE_AGENCY) {
            $fields = array_merge($fields, [
                StructuredJobAd::KEY_EMPLOYER_LOGO_LOCATION,
            ]);
        }

        return [
            ...$fields,
            ...self::getRequiredFields(),
        ];
    }

    public static function getAcceptedCreatives(): array
    {
        return [
            StructuredJobAd::CREATIVE_LANDING,
            StructuredJobAd::CREATIVE_IMAGE,
        ];
    }

    public function setPerPage(int $perPage): self
    {
        $this->perPage = $perPage;

        return $this;
    }

    public function getContentType(): string
    {
        return 'application/json';
    }

    public function getSingleAdOutput(StructuredJobAd $ad)
    {
        $keepVisible = $this->getKeepVisible();
        $this->setFieldsOnSingleAd($ad, $keepVisible);

        return $ad;
    }

    public function setFieldsOnSingleAd(StructuredJobAd $ad, array $keepVisible): void
    {
        if ($ad->landing) {
            $hidden = [
                'data',
                'published_data',
                'image_url',
                'public_token',
                'private_token',
                'email',
            ];
            $ad->landing->setHidden(array_diff($hidden, $keepVisible));
        }
        if ($ad->image) {
            $ad->image->setHidden([
                'type',
                'location',
                'fileable_id',
                'fileable_type',
                'project_id',
            ]);
        }
        if ($ad->emotionimages->count()) {
            $ad->emotionimages->each(function (File $emotionImage) {
                $emotionImage->setVisible([
                    'url',
                    'display_name',
                    'created_at',
                    'updated_at',
                    'sort_order',
                ]);
            });
        }
        $ad->append(['employer_logo_url']);
        $ad->setHidden([
            'project_id',
        ]);

        if ($ad->project) {
            $ad->project->append(['display_custom_fields']);
            if ($team = $ad->project->team) {
                $team->append(['logo_url']);
                $team->setHidden(['x', 'y']);
            }
        }
    }

    /**
     * @return array|string[]
     */
    public function getKeepVisible(): array
    {
        $keepVisible = [];
        if ($extraFieldsStr = request('fields')) {
            $keepVisible = explode(',', $extraFieldsStr);
        }

        return $keepVisible;
    }
}
