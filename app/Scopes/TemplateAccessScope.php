<?php

namespace App\Scopes;

use App\Helpers;
use App\Services\Teams\TeamGraphResolver;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class TemplateAccessScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        $user = Helpers::getScopeUser();
        if ($user) {
            if ($user->can_see_all_projects) {
                return;
            }

            $builder->where(function ($q) use ($user) {
                /** @var TeamGraphResolver $resolver */
                $resolver = app(TeamGraphResolver::class);

                if (!$user->team_id) {
                    $visibleTemplateTeamIds = [];
                } else {
                    $visibleTemplateTeamIds = [
                        ...$user->accessible_team_ids, // lords must access descendant team templates,
                    ];
                }

                // First check if the template has a user_id set
                $q->where(function ($q1) use ($visibleTemplateTeamIds, $user) {
                    // If user_id is set, only that specific user can see the template
                    $q1->where(function ($q2) use ($user) {
                        $q2->whereNotNull('user_id')
                            ->where('user_id', $user->id);
                    });

                    // If user_id is null, apply team-based visibility rules
                    $q1->orWhere(function ($q2) use ($visibleTemplateTeamIds) {
                        $q2->whereNull('user_id')
                            ->where(function ($q3) use ($visibleTemplateTeamIds) {
                                $q3->whereNull('team_id')
                                    ->orWhereIn('team_id', [
                                        0, // safety in case the above are empty
                                        ...$visibleTemplateTeamIds,
                                    ]);
                            });
                    });
                });
            });
        }
    }
}
