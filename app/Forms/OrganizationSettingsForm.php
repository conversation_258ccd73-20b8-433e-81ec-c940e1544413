<?php

namespace App\Forms;

use App\Classes\Mail\MailerBuilder;
use App\Forms\Traits\MessageHelper;
use App\Forms\Traits\StageCategoryFields;
use App\Helpers;
use App\Models\File;
use App\Models\Landing;
use App\Models\Setting;
use App\Models\User;
use App\Models\Website;
use App\Services\SSO\SSO;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Laraform\Laraform;
use libphonenumber\CountryCodeToRegionCodeMap;

class OrganizationSettingsForm extends Laraform
{
    const YES = 'yes';
    const NO = 'no';

    public $model = User::class;

    public $component = 'base-form ref="bform"';

    public $endpoint = '/organization/settings';

    public $validateOn = 'submit';

    public $items;

    use MessageHelper, StageCategoryFields;

    public function tabs()
    {
        $website = Helpers::getCurrentWebsite();

        $tabs = [
            'general' => [
                'label' => __('General'),
                'elements' => [
                    Setting::KEY_LANGUAGE,
                    Setting::KEY_LOCALE,
                    Setting::KEY_MANDATORY_2FA,
                    Setting::KEY_ORGANIZATION_TYPE,
                    Setting::KEY_TIMEZONE,
                    Setting::KEY_ORGANIZATION_ADDRESS,
                    Setting::KEY_PHONE_DEFAULT_COUNTRY_CODE,
                    ...($website->features[Website::FEATURE_JOB_ADS_IP_LIMIT] ? [Setting::KEY_INTERNAL_NETWORK_ADDR_INFO] : []),
                    ...($website->features[Website::FEATURE_ENABLE_AI] ? [Setting::KEY_AI_PROVIDER] : []),
                ],
            ],
            'project' => [
                'label' => __('Project'),
                'elements' => [
                    Setting::KEY_DEFAULT_STAGES,
                    Setting::KEY_USE_CUSTOM_STAGE_CATEGORIES,
                    Setting::KEY_SEND_TENTATIVE_SCHEDULER_SLOTS,
                    Setting::KEY_AUTOMATICALLY_ARCHIVE_LANDING_PAGES,
                    Setting::KEY_LANDING_HIDE_ARCHIVED_PERMANENTLY,
                    Setting::KEY_SHOW_ALL_PROJECT_LOGS_TO_LIMITED,
                    Setting::KEY_PROJECTS_PRIVATE_BY_DEFAULT,
                    Setting::KEY_USE_PROJECT_LOCATIONS_FEATURE,
                    Setting::KEY_COMMENTS_PUBLIC_BY_DEFAULT,
                    Setting::KEY_PROJECT_CUSTOM_FIELDS,
                    Setting::KEY_CANDIDATE_CUSTOM_FIELDS,
                    'section_candidate_card',
                    Setting::KEY_CC_LAST_EMPLOYMENT,
                    Setting::KEY_CC_LAST_EDUCATION,
                    Setting::KEY_CC_TAGS,
                    Setting::KEY_CC_INDICATOR_INVITES,
                    Setting::KEY_CC_INDICATOR_VIDEO_INVITES,
                    ...($website->features[Website::FEATURE_REFERENCES] ? [Setting::KEY_CC_INDICATOR_REFERENCES] : []),
                    Setting::KEY_CC_INDICATOR_COMMENTS,
                    Setting::KEY_CC_INDICATOR_MESSAGES,
                    Setting::KEY_CC_EMAIL,
                    Setting::KEY_CC_PHONE,
                    Setting::KEY_CC_OTHER_ACTIVE_CANDIDACIES,
                    Setting::KEY_CC_SOURCE,
                    Setting::KEY_MARK_UNDERAGE_CANDIDATES,
                    Setting::KEY_UNDERAGE_AGE_UNDER,
                    Setting::KEY_CC_SHOW_INITIALS_IN_BLIND_MODE,
                    Setting::KEY_CC_LOCATION,
                    Setting::KEY_CC_TASKS_FROM_ALL_PROJECTS,
                    Setting::KEY_HIDE_TAGS_FROM_LIMITED_USERS,
                    'section_user_access_controls',
                    Setting::KEY_ALLOW_REGULAR_USERS_CREATE_TAGS,
                    Setting::KEY_HIDE_MESSAGES_FROM_LIMITED_USERS,
                    Setting::KEY_SHOW_ALL_PROJECTS_TO_LIMITED_USERS,
                    Setting::KEY_ENABLE_SCHEDULING_FOR_LIMITED_USERS,
                    Setting::KEY_ALLOW_WITHOUT_EMAIL,
                    Setting::KEY_ALLOW_WITHOUT_LOCATION,
                    Setting::KEY_MAKE_IMPORT_COMMENTS_PUBLIC,
                    Setting::KEY_ASK_FOR_DROPOUT_REASON,
                    Setting::KEY_HIDE_FILES_FROM_LIMITED_USERS,
                    Setting::KEY_INHERIT_CANDIDATE_FILE_ACCESS_FROM_PROJECT,
                    ...($website->features[Website::FEATURE_KEY_TEAMS] ? [Setting::KEY_ALWAYS_INHERIT_CANDIDATE_ACCESS_FROM_PROJECT] : []),
                ],
            ],
            'mail' => [
                'label' => __('Mail'),
                'elements' => [
                    Setting::KEY_TEMPLATE_INVITE,
                    Setting::KEY_INSTANCE_EMAIL_PROVIDER,
                    Setting::KEY_ENABLE_EMAIL_CLICK_TRACKING,
                    Setting::KEY_INSTANCE_SMTP_SETTINGS,
                    Setting::KEY_ENABLE_SEND_MAIL_AS_USER,
                ],
            ],
            'brand' => [
                'label' => __('Brand'),
                'elements' => [
                    Setting::KEY_ORGANIZATION_NAME,
                    Setting::KEY_ORGANIZATION_CITY,
                    Setting::KEY_ORGANIZATION_REGISTRY_CODE,
                    Setting::KEY_ORGANIZATION_STREET_ADDRESS,
                    Setting::KEY_ORGANIZATION_LOGO,
                    ...($website->features[Website::FEATURE_FAVICON] ? [Setting::KEY_ORGANIZATION_FAVICON] : []),
                    Setting::KEY_ORGANIZATION_WEBSITE_URL,
                    Setting::KEY_BRAND_VIDEO_URL,
                    Setting::KEY_BRAND_ABOUT,
                    Setting::KEY_DEFAULT_CAREERS_PAGE_LANDING_ID,
                    Setting::KEY_LANDING_TEMPLATE,
                    'section_candidate_survey',
                    Setting::KEY_SURVEY_QUESTION,
                    'feedback_form_teaser',
                    'section_signature',
                    Setting::KEY_SIGNATURE_ACCENT_COLOR,
                    Setting::KEY_SIGNATURE_STATIC_TEXT,
                    'signature_preview',
                ],
            ],
            'gdpr' => [
                'label' => __('GDPR'),
                'elements' => [
                    Setting::KEY_PRIVACY_POLICY_URL,
                    Setting::KEY_CONSENT_REQUEST_VALID_DAYS,
                    Setting::KEY_CONSENT_VALID_MONTHS,
                    Setting::KEY_CONSENT_DISPUTE_MONTHS,
                    Setting::KEY_CONSENT_AUTOMATION_ENABLED,
                    Setting::KEY_CONSENT_RENEWAL_SUBJECT,
                    Setting::KEY_CONSENT_RENEWAL_BODY,
                    Setting::KEY_CONSENT_ADMIN_USER_ID,
                    Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS,
                    Setting::KEY_ENABLE_PERMANENT_DELETE,
                ],
            ],
        ];
        if ($website->features[Website::FEATURE_KEY_REQUISITIONS]) {
            $tabs['requisitions'] = [
                'label' => __('Requisitions'),
                'elements' => [
                    Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS,
                    Setting::KEY_REQUISITION_MIN_USERS_APPROVAL,
                    Setting::KEY_REQUISITION_CUSTOM_FIELDS,
                    Setting::KEY_REQUISITIONS_INFO_MESSAGE,
                ],
            ];
        }
        if ($website->features[Website::FEATURE_KEY_SSO]) {
            $tabs['sso'] = [
                'label' => __('SSO'),
                'elements' => [
                    'sso_setup',
                    Setting::KEY_SSO_ALLOW_PASSWORD_LOGINS,
                ],
            ];
        }

        return $tabs;
    }

    public function schema()
    {
        $website = Helpers::getCurrentWebsite();

        return [
            Setting::KEY_LANGUAGE => [
                'type' => 'select',
                'label' => __('Application language'),
                'description' => __('The default language for new users in your organization.'),
                'items' => [
                    'en' => 'English',
                    'et' => 'Eesti keel (Estonian)',
                    'lv' => 'Latviešu (Latvian)',
                    'lt' => 'Lietuvių (Lithuanian)',
                    ...($website->features[Website::FEATURE_LANG_RU] ? ['ru' => 'Русский (Russian)'] : []),
                ],
                'default' => 'en',
            ],
            Setting::KEY_LOCALE => [
                'type' => 'select',
                'label' => __('Application date and time format'),
                'description' => __('The default date and time format for your organization.'),
                'items' => [
                    'en-GB' => __('English (UK)'),
                    'en-US' => __('English (US)'),
                    'et-EE' => __('Estonia'),
                    'fi-FI' => __('Finland'),
                    'hu-HU' => __('Hungary'),
                    'lv-LV' => __('Latvia'),
                    'lt-LT' => __('Lithuania'),
                ],
                'default' => Helpers::getDefaultLocaleFromWebsite(),
            ],
            Setting::KEY_ORGANIZATION_TYPE => [
                'type' => 'select',
                'label' => __('Organization type'),
                'description' => __('Agency has clients, corporation has managers.'),
                'items' => [
                    Setting::ORGANIZATION_TYPE_CORPORATION => __('Corporation'),
                    Setting::ORGANIZATION_TYPE_AGENCY => __('Agency'),
                ],
            ],
            Setting::KEY_TIMEZONE => [
                'type' => 'select',
                'label' => __('Organization timezone'),
                'description' => __('This is used for all time inputs and outputs. '),
                'items' => Helpers::getTimezones(),
                'placeholder' => __('Type to search...'),
            ],
            Setting::KEY_ORGANIZATION_LOGO => [
                'type' => 'file',
                'label' => __('Organization logo'),
                'folder' => 'uploads',
                'disk' => 'public',
                'url' => preg_replace('/\/$/', '', Storage::url('')),
                'store' => function (UploadedFile $file, $entity) {
                    $location = File::store($file);

                    return [
                        Setting::KEY_ORGANIZATION_LOGO => $location,
                    ];
                },
                'description' => __('Used in email signatures, job board exports. PNG format suggested.'),
            ],
            ...($website->features[Website::FEATURE_FAVICON] ? [Setting::KEY_ORGANIZATION_FAVICON => [
                'type' => 'file',
                'label' => __('Organization favicon'),
                'folder' => 'uploads',
                'disk' => 'public',
                'url' => preg_replace('/\/$/', '', Storage::url('')),
                'store' => function (UploadedFile $file, $entity) {
                    $location = File::store($file);

                    return [
                        Setting::KEY_ORGANIZATION_FAVICON => $location,
                    ];
                },
                'description' => __('Favicon is the small icon that appears in the browser tab on your landing pages.'),
            ]] : []),
            Setting::KEY_ORGANIZATION_WEBSITE_URL => [
                'type' => 'text',
                'label' => __('Org. website url'),
                'description' => __('Used in email signatures, job board exports.'),
            ],
            Setting::KEY_ORGANIZATION_NAME => [
                'type' => 'text',
                'label' => __('Org. name'),
                'rules' => 'nullable',
                'description' => __('Used in job board exports.'),
            ],
            Setting::KEY_ORGANIZATION_REGISTRY_CODE => [
                'type' => 'text',
                'label' => __('Org. registry code'),
                'rules' => 'nullable',
                'description' => __('Used in job board exports.'),
            ],
            Setting::KEY_LANDING_HOSTNAME_ID => [
                'type' => 'select',
                'label' => __('Job ad landing page hostname'),
                'rules' => 'nullable',
                'items' => Helpers::modelsToOptions(Helpers::getCurrentWebsite()->hostnames, 'fqdn'),
            ],
            Setting::KEY_ORGANIZATION_CITY => [
                'type' => 'text',
                'label' => __('Org. location city'),
                'rules' => 'nullable',
                'description' => __('Used in job board exports.'),
            ],
            Setting::KEY_ORGANIZATION_STREET_ADDRESS => [
                'type' => 'text',
                'label' => __('Org. street address'),
                'rules' => 'nullable',
                'description' => __('Used in job board exports.'),
            ],
            Setting::KEY_PRIVACY_POLICY_URL => [
                'type' => 'text',
                'label' => __('Privacy policy URL'),
                'rules' => 'nullable|url',
                'description' => __('Used for GDPR compliance'),
            ],
            Setting::KEY_ORGANIZATION_ADDRESS => [
                'type' => 'text',
                'label' => __('Organization office address'),
            ],
            'section_project' => [
                'type' => 'static',
                'content' => '<hr><div class="field-name-text mb-2">Project settings</div>',
                'columns' => ['label' => 0, 'field' => 12],
            ],
            Setting::KEY_DEFAULT_STAGES => [
                'type' => 'list',
                'label' => __('Default stages'),
                'add_btn_label' => __('Add stage'),
                'sort' => true,
                'description' => __('When creating new projects, these stages are used by default.'),
                'object' => [
                    'columns' => [
                        'label' => 0,
                        'field' => 12,
                    ],
                    //                    'class' => 'bg-gray-light border border-gray mb-2 pl-3 pr-3 pt-1 rounded',
                    'schema' => [
                        'name' => [
                            'label' => __('Name'),
                            'type' => 'text',
                            'rules' => 'required',
                        ],
                        ...$this->getStageCategoryFields(),
                    ],
                ],
            ],
            Setting::KEY_USE_CUSTOM_STAGE_CATEGORIES => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Use custom stage categories'),
            ],
            Setting::KEY_SEND_TENTATIVE_SCHEDULER_SLOTS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Send tentative scheduler time slots to interviewers'),
            ],
            Setting::KEY_ALLOW_WITHOUT_EMAIL => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Allow candidates without email'),
                'description' => __('This option allows you to add candidates without email. This will make some features fail.'),
            ],
            Setting::KEY_ASK_FOR_DROPOUT_REASON => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Prompt for a reason when a candidate drops out'),
            ],
            Setting::KEY_PROJECT_CUSTOM_FIELDS => [
                'type' => 'list',
                'label' => __('Project custom fields'),
                'sort' => true,
                'initial' => 0,
                'element' => [
                    'type' => 'field',
                    'columns' => ['label' => 0, 'field' => 12],
                    'parentKey' => Setting::KEY_PROJECT_CUSTOM_FIELDS,
                ],
            ],
            ...($website->features[Website::FEATURE_KEY_REQUISITIONS] ? [
                Setting::KEY_REQUISITION_CUSTOM_FIELDS => [
                    'type' => 'list',
                    'label' => __('Requisition custom fields'),
                    'initial' => 0,
                    'object' => [
                        // 'class' => 'bg-gray-light border border-gray mb-2 pl-3 pr-3 pt-3 rounded',
                        'columns' => ['label' => 0, 'field' => 12],
                        'schema' => [
                            ...$this->getCustomFieldObjectCommonFields(Setting::KEY_REQUISITION_CUSTOM_FIELDS),
                        ],
                    ],
                ],
                Setting::KEY_REQUISITIONS_INFO_MESSAGE => [
                    'type' => 'trix',
                    'label' => __('Requisition info field template'),
                ],
                Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS => [
                    'type' => 'tags',
                    'label' => __('Always ask approval from these users'),
                    'items' => User::getForForm(),
                ],
                Setting::KEY_REQUISITION_MIN_USERS_APPROVAL => [
                    'type' => 'text',
                    'label' => __('Minimum amount of users needed approval from'),
                    'rules' => 'required|numeric|min:0',
                ],
            ] : []),
            Setting::KEY_CANDIDATE_CUSTOM_FIELDS => [
                'type' => 'list',
                'label' => __('Candidate custom fields'),
                'sort' => true,
                'initial' => 0,
                'object' => [
                    // 'class' => 'bg-gray-light border border-gray mb-2 pl-3 pr-3 pt-3 rounded',
                    'columns' => ['label' => 0, 'field' => 12],
                    'schema' => [
                        ...$this->getCustomFieldObjectCommonFields(Setting::KEY_CANDIDATE_CUSTOM_FIELDS),
                        'show_on_candidate_card' => [
                            'type' => 'toggle',
                            'label' => __('Show on candidate card'),
                        ],
                        'visibility' => [
                            'type' => 'select',
                            'label' => __('Visibility'),
                            'default' => null,
                            'items' => [
                                ['value' => null, 'label' => __('Visible to all')],
                                ['value' => 'hidden', 'label' => __('Hidden')],
                            ],
                        ],
                    ],
                ],
            ],
            'section_mail' => [
                'type' => 'static',
                'content' => '<hr><div class="field-name-text mb-2">' . __('Mail settings') . '</div>',
                'columns' => ['label' => 0, 'field' => 12],
            ],
            Setting::KEY_TEMPLATE_INVITE => [
                'type' => 'trix',
                'label' => __('New user invite template'),
                'after' => __('This e-mail is sent when you add a new user to your organization.') . ' ' .
                    __('It MUST include [password_url]. It may include [current_user_name], [user_name].'),
                'rules' => ['regex:/\[password_url]/'],
            ],
            Setting::KEY_INSTANCE_EMAIL_PROVIDER => [
                'type' => 'select',
                'label' => __('Instance email provider'),
                'description' => __('Please contact support for custom e-mail setups.'),
                'disabled' => Setting::get(Setting::KEY_INSTANCE_EMAIL_DOMAIN) ? 'disabled' : null,
                'items' => [
                    ['label' => __('Postmark'), 'value' => MailerBuilder::PROVIDER_POSTMARK],
                    ['label' => __('SES'), 'value' => MailerBuilder::PROVIDER_SES],
                    ['label' => __('Bring your own SMTP'), 'value' => 'custom'],
                ],
            ],
            Setting::KEY_ENABLE_EMAIL_CLICK_TRACKING => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Enable click tracking'),
                'conditions' => [
                    [Setting::KEY_INSTANCE_EMAIL_PROVIDER, [MailerBuilder::PROVIDER_SES, MailerBuilder::PROVIDER_POSTMARK]],
                ],
            ],
            Setting::KEY_INSTANCE_SMTP_SETTINGS => [
                'type' => 'object',
                'conditions' => [
                    [Setting::KEY_INSTANCE_EMAIL_PROVIDER, '=', 'custom'],
                ],
                'schema' => [
                    'transport' => [
                        'type' => 'hidden',
                        'default' => 'smtp',
                    ],
                    'host' => [
                        'type' => 'text',
                        'label' => __('Host'),
                    ],
                    'port' => [
                        'type' => 'text',
                        'label' => __('Port'),
                    ],
                    'encryption' => [
                        'type' => 'select',
                        'label' => __('Encryption'),
                        'items' => ['tls' => 'TLS', 'ssl' => 'SSL'],
                    ],
                    'username' => [
                        'type' => 'text',
                        'label' => __('Username'),
                    ],
                    'password' => [
                        'type' => 'text',
                        'label' => __('Password'),
                    ],
                    'events_from_address' => [
                        'type' => 'text',
                        'label' => __('Calendar invites FROM email address'),
                        'description' => __('Leave empty if not applicable'),
                    ],
                ],
            ],
            Setting::KEY_ENABLE_SEND_MAIL_AS_USER => [
                'type' => 'select',
                'label' => __('Enable "Send as user" for messages'),
                'items' => [
                    Setting::SEND_AS_USER_DISABLED => __('Disabled'),
                    Setting::SEND_AS_USER_ONLY_SERVICE_ACCOUNTS => __('Only from service accounts'),
                    Setting::SEND_AS_USER_ANY_ACCOUNT => __('Any account'),
                ],
            ],
            Setting::KEY_LANDING_TEMPLATE => [
                'type' => 'hidden',
            ],
            'section_candidate_card' => [
                'type' => 'static',
                'content' => '<hr><div class="field-name-text mb-2">' . __('Candidate card settings') . '</div>',
                'columns' => ['label' => 0, 'field' => 12],
            ],
            Setting::KEY_CC_LAST_EMPLOYMENT => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Last employment'),
            ],
            Setting::KEY_CC_LAST_EDUCATION => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Last education'),
            ],
            Setting::KEY_CC_TAGS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Tags'),
            ],
            Setting::KEY_CC_INDICATOR_INVITES => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show pending/scheduled interview indicator'),
            ],
            Setting::KEY_CC_INDICATOR_VIDEO_INVITES => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show pending/answered video interview indicator'),
            ],
            ...($website->features[Website::FEATURE_REFERENCES] ? [
                Setting::KEY_CC_INDICATOR_REFERENCES => [
                    'type' => 'toggle',
                    'rules' => 'boolean',
                    'label' => __('Show pending/submitted references indicator'),
                ],
            ] : []),
            Setting::KEY_CC_INDICATOR_COMMENTS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show comments indicator'),
            ],
            Setting::KEY_CC_INDICATOR_MESSAGES => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show messages indicator'),
            ],
            Setting::KEY_CC_EMAIL => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show email'),
            ],
            Setting::KEY_CC_PHONE => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show phone'),
            ],
            Setting::KEY_CC_OTHER_ACTIVE_CANDIDACIES => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show if candidate is active in other projects'),
            ],
            Setting::KEY_CC_SOURCE => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show source'),
            ],
            Setting::KEY_CC_SHOW_INITIALS_IN_BLIND_MODE => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show candidate initials in blind hiring mode'),
            ],
            Setting::KEY_CC_LOCATION => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show location'),
            ],
            Setting::KEY_CC_TASKS_FROM_ALL_PROJECTS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show tasks from all projects'),
            ],
            Setting::KEY_MARK_UNDERAGE_CANDIDATES => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Mark underage candidates'),
            ],
            Setting::KEY_UNDERAGE_AGE_UNDER => [
                'type' => 'text',
                'rules' => 'integer|min:1|max:100',
                'label' => __('Underage threshold (years)'),
                'description' => __('Candidates below this age will be marked as underage on the candidate card.'),
                'conditions' => [
                    [Setting::KEY_MARK_UNDERAGE_CANDIDATES, true],
                ],
            ],
            'section_user_access_controls' => [
                'type' => 'static',
                'content' => '<hr><div class="field-name-text mb-2">' . __('Access controls') . '</div>',
                'columns' => ['label' => 0, 'field' => 12],
            ],
            Setting::KEY_HIDE_TAGS_FROM_LIMITED_USERS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Hide tags from limited users'),
            ],
            Setting::KEY_HIDE_FILES_FROM_LIMITED_USERS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Hide files from limited users (except anonymized versions)'),
            ],
            Setting::KEY_INHERIT_CANDIDATE_FILE_ACCESS_FROM_PROJECT => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Inherit candidate file access rights from projects.'),
                'description' => __('When this is turned off, candidate files from all projects are visible to all users.'),
            ],
            ...($website->features[Website::FEATURE_KEY_TEAMS] ? [
                Setting::KEY_ALWAYS_INHERIT_CANDIDATE_ACCESS_FROM_PROJECT => [
                    'type' => 'toggle',
                    'rules' => 'boolean',
                    'label' => __('Private talent pool for teams'),
                    'description' => __('This inherits all access rights to candidate profiles from projects.'),
                ],
            ] : []),
            Setting::KEY_ALLOW_REGULAR_USERS_CREATE_TAGS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Allow regular users to create tags'),
            ],
            Setting::KEY_HIDE_MESSAGES_FROM_LIMITED_USERS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Hide messages from limited users'),
            ],
            Setting::KEY_SHOW_ALL_PROJECTS_TO_LIMITED_USERS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show all projects to limited users'),
                'description' => __('Limited users can see the same projects as regular users. Stages hidden from limited users stay hidden.'),
            ],
            Setting::KEY_ENABLE_SCHEDULING_FOR_LIMITED_USERS => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Allow limited users to schedule interviews'),
            ],
            Setting::KEY_MANDATORY_2FA => [
                'type' => 'toggle',
                'label' => __('Mandatory two-factor authentication'),
                'description' => __('All users of this instance must verify their phone numbers.'),
            ],
            Setting::KEY_ALLOW_WITHOUT_LOCATION => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Allow candidates without location'),
                'description' => __('This option allows you to require adding a location for each candidate.'),
            ],
            Setting::KEY_SHOW_ALL_PROJECT_LOGS_TO_LIMITED => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Show non-public project logs to limited users'),
            ],
            Setting::KEY_PROJECTS_PRIVATE_BY_DEFAULT => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Initialize project visibility switch as private'),
            ],
            Setting::KEY_USE_PROJECT_LOCATIONS_FEATURE => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Use project locations feature'),
            ],
            Setting::KEY_COMMENTS_PUBLIC_BY_DEFAULT => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Initialize comment visibility switch as public'),
            ],
            Setting::KEY_MAKE_IMPORT_COMMENTS_PUBLIC => [
                'type' => 'toggle',
                'rules' => 'boolean',
                'label' => __('Make import and application form submission comments visible to limited users'),
            ],
            Setting::KEY_BRAND_VIDEO_URL => [
                'type' => 'text',
                'rules' => 'url|nullable',
                'label' => __('Employer brand video'),
                'description' => __('Only YouTube URLs. Used for job board exports.'),
            ],
            Setting::KEY_BRAND_ABOUT => [
                'type' => 'textarea',
                'rules' => 'max:5000',
                'label' => __('About employer'),
                'description' => __('Used for job board exports.'),
            ],
            Setting::KEY_DEFAULT_CAREERS_PAGE_LANDING_ID => [
                'type' => 'select',
                'label' => __('Default careers page'),
                'rules' => 'nullable',
                'items' => [
                    ['value' => null, 'label' => __('None')],
                    ...Helpers::modelsToOptions(Landing::query()
                        ->where('status', Landing::STATUS_ACTIVE)
                        ->get()
                        ->filter(function ($landing) {
                            return collect($landing->data['blocks'] ?? [])
                                ->filter(fn ($b) => $b['type'] === 'LandingCareersBlock')
                                ->count() > 0;
                        }), 'display_name')],
                'description' => __('This landing page appears at :url', ['url' => Helpers::getCurrentTenantUri('/')]),
            ],
            Setting::KEY_CONSENT_REQUEST_VALID_DAYS => [
                'type' => 'text',
                'label' => __('Default data processing consent request validity duration'),
                'rules' => 'numeric',
                'description' => __('Duration in days'),
            ],
            Setting::KEY_CONSENT_VALID_MONTHS => [
                'type' => 'text',
                'label' => __('Default data processing consent duration'),
                'rules' => 'numeric',
                'description' => __('Duration in months'),
            ],
            Setting::KEY_CONSENT_DISPUTE_MONTHS => [
                'type' => 'text',
                'label' => __('Dispute months'),
                'rules' => 'numeric',
            ],
            Setting::KEY_CONSENT_AUTOMATION_ENABLED => [
                'type' => 'toggle',
                'label' => __('Data processing consent automation'),
                'after' => Helpers::getGdprAutomationExplainer() . Helpers::getGdprMemo(),
            ],
            Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS => [
                'type' => 'toggle',
                'label' => __('Send consent renewal messages'),
                'after' => __('If enabled, GDPR automation sends out "consent renewal request" emails.'),
                'conditions' => [
                    [Setting::KEY_CONSENT_AUTOMATION_ENABLED, true],
                ],
            ],
            Setting::KEY_CONSENT_RENEWAL_SUBJECT => [
                'type' => 'text',
                'label' => __('Consent renewal message subject'),
                'rules' => 'required',
                'conditions' => [
                    [Setting::KEY_CONSENT_AUTOMATION_ENABLED, true],
                ],
            ],
            Setting::KEY_CONSENT_RENEWAL_BODY => [
                'type' => 'trix',
                'label' => __('Consent renewal message body'),
                'rules' => 'regex:/\[consent_renewal_url]/',
                'after' => $this->getMergeTagInfo('consent_renewal_url', withSurvey: false),
                'clickable_merge_tags' => true,
                'signature_user_id_path' => Setting::KEY_CONSENT_ADMIN_USER_ID,
                'conditions' => [
                    [Setting::KEY_CONSENT_AUTOMATION_ENABLED, true],
                ],
            ],
            Setting::KEY_CONSENT_ADMIN_USER_ID => [
                'type' => 'select',
                'label' => __('GDPR admin'),
                'conditions' => [
                    [Setting::KEY_CONSENT_AUTOMATION_ENABLED, true],
                ],
                'rules' => 'required',
                'description' => __('All GDPR messages to candidates will be sent from this user.'),
                'items' => User::query()
                    ->orderBy('name')
                    ->active()
                    ->where('role', User::ROLE_ADMIN)
                    ->where('email', 'not ilike', '%@recruitlab.%')
                    ->get()->mapWithKeys(function ($user) {
                        return [
                            $user->id => $user->name,
                        ];
                    })->toArray(),
            ],
            Setting::KEY_ENABLE_PERMANENT_DELETE => [
                'type' => 'toggle',
                'label' => __('Enable permanent delete'),
                'after' => __('By default, candidate profiles are pseudonymized using a one-way cryptographic function.  When they re-apply then their history (comments, projects) is restored. If you enable permanent deletion then candidate histories will not be restored.'),
            ],
            Setting::KEY_PHONE_DEFAULT_COUNTRY_CODE => [
                'type' => 'select',
                'label' => __('Default phone country code'),
                'items' => collect(CountryCodeToRegionCodeMap::$countryCodeToRegionCodeMap)
                    ->flatten()
                    ->values()
                    ->mapWithKeys(fn ($v) => [$v => $v])
                    ->toArray(),
            ],
            ...($website->features[Website::FEATURE_ENABLE_AI] ? [
                Setting::KEY_AI_PROVIDER => [
                    'type' => 'select',
                    'label' => __('AI Provider'),
                    'items' => [
                        Setting::AI_PROVIDER_OPENAI => __('OpenAI (US-based)'),
                        Setting::AI_PROVIDER_AZURE_EUR => __('Azure OpenAI (data never leaves EU)'),
                    ],
                ],
            ] : []),
            ...($website->features[Website::FEATURE_JOB_ADS_IP_LIMIT] ? [
                Setting::KEY_INTERNAL_NETWORK_ADDR_INFO => [
                    'type' => 'text',
                    'label' => __('Company network gateway IP'),
                    'description' => __('Comma separated IP addresses or CIDR ranges.'),
                ]] : []
            ),
            'sso_setup' => [
                'type' => 'static',
                'content' => !SSO::enabled() ? Helpers::getSSOSetupCTA() : '<div class="alert alert-success">' . __('SSO is enabled!') . '</div>',
                'columns' => ['label' => 0, 'field' => 12],
            ],
            Setting::KEY_SSO_ALLOW_PASSWORD_LOGINS => [
                'type' => (SSO::enabled() || count(SSO::enabledConfigs())) ? 'toggle' : 'hidden',
                'label' => __('Allow password logins'),
                'description' => __('Useful for collaborating with external recruitment partners. After enabling this setting, an admin can switch on password access for specific user accounts.'),
            ],
            'signature_user_id' => [
                'type' => 'meta',
                'default' => auth()->id(),
            ],
            'section_candidate_survey' => [
                'type' => 'heading',
                'heading' => 'h3',
                'content' => __('cNPS survey'),
            ],
            Setting::KEY_SURVEY_QUESTION => [
                'type' => 'text',
                'label' => __('Candidate survey question'),
                'rules' => 'nullable',
            ],
            'feedback_form_teaser' => [
                'type' => 'static',
                'label' => '',
                'content' => Helpers::getSurveyFormTeaser(),
            ],
            'section_signature' => [
                'type' => 'heading',
                'heading' => 'h3',
                'content' => __('Company mail signature settings'),
                'description' => __('Applies to all user signatures.'),
            ],
            Setting::KEY_SIGNATURE_ACCENT_COLOR => [
                'type' => 'swatch',
                'label' => __('Signature accent color'),
                'rules' => 'required',
                'description' => __('Used in email signatures'),
            ],
            Setting::KEY_SIGNATURE_STATIC_TEXT => [
                'type' => 'trix',
                'label' => __('Signature additional text'),
                'description' => __('Use this for confidentiality notices, disclaimers, etc.'),
                'add_link_to_attachments' => false,
            ],
            'signature_preview' => [
                'type' => 'trix',
                'label' => __('Signature preview'),
                'disabled' => true,
                'signature' => auth()->user()->signature,
                'signature_user_id_path' => 'signature_user_id',
                'signature_watch_fields' => [
                    Setting::KEY_ORGANIZATION_LOGO,
                    Setting::KEY_ORGANIZATION_WEBSITE_URL,
                    Setting::KEY_SIGNATURE_ACCENT_COLOR,
                    Setting::KEY_ORGANIZATION_NAME,
                    Setting::KEY_SIGNATURE_STATIC_TEXT,
                ],
                'clickable_merge_tags' => false,
            ],
            Setting::KEY_AUTOMATICALLY_ARCHIVE_LANDING_PAGES => [
                'type' => 'toggle',
                'label' => __('Automatically archive landing pages after application deadline'),
                'description' => __('Automatically archives landing pages after their application deadlines have passed. Applies retroactively to old landing pages.'),
            ],
            Setting::KEY_LANDING_HIDE_ARCHIVED_PERMANENTLY => [
                'type' => 'toggle',
                'label' => __('Hide archived landing pages from public'),
                'description' => __(''),
            ],
        ];
    }

    public function getCustomFieldObjectCommonFields(string $parentKey): array
    {
        return [
            'type' => [
                'label' => __('Field type'),
                'type' => 'select',
                'default' => 'select',
                'items' => [
                    'select' => __('Select'),
                    'text' => __('Text'),
                    'datetime' => __('Datetime'),
                    'checkbox' => __('Checkbox'),
                    'tags' => __('Multiselect'),
                ],
            ],
            'label' => [
                'type' => 'text',
                'label' => __('Label'),
                'rules' => 'required',
            ],
            'items' => [
                'label' => __('Values'),
                'type' => 'list',
                'sort' => true,
                'rules' => 'min:1',
                'class' => 'inlined-list',
                'element' => [
                    'type' => 'text',
                    'columns' => ['label' => 0, 'field' => 12],
                    'placeholder' => __('Custom option value'),
                    'rules' => 'required',
                ],
                'conditions' => [
                    ["$parentKey.*.type", ['select', 'tags']],
                ],
            ],
            'original_slug' => [
                'type' => 'hidden',
            ],
            'rules' => [
                'type' => 'tags',
                'label' => __('Rules'),
                'items' => [
                    ['value' => 'required', 'label' => __('Field must be filled')],
                    ['value' => 'nullable', 'label' => __('Field can be empty')],
                    ['value' => 'integer', 'label' => __('Value must be a number')],
                    ['value' => 'url', 'label' => __('Field must be valid url')],
                ],
            ],
        ];
    }

    public function before() {}

    public function after() {}
}
