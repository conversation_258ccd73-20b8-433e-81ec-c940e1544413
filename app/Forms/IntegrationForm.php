<?php

namespace App\Forms;

use App\Classes\AdminSlackAlerter;
use App\Helpers;
use App\Models\Form;
use App\Models\Integration;
use App\Models\StructuredJobAd;
use App\Models\User;
use App\Models\Website;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Laraform\Laraform;
use Segment\Segment;
use Str;

class IntegrationForm extends Laraform
{
    public $model = Integration::class;

    public $component = 'base-form ref="bform"';

    public $endpoint = '/laraform/process';

    public function schema()
    {
        $integrationTypes = collect(Integration::getTypes())
            ->when(
                auth()->user()->role !== User::ROLE_ADMIN,
                fn (Collection $types) => $types->only(Integration::INTEGRATION_TYPES_WITH_MANDATORY_USER)
            )
            ->all();

        natcasesort($integrationTypes);

        return [
            'id' => [
                'type' => 'key',
            ],
            'remote_type' => [
                'type' => 'select',
                'items' => $integrationTypes,
                'label' => __('Type'),
            ],
            'name' => [
                'type' => 'text',
                'label' => __('Integration name'),
                'rules' => 'required',
            ],
            'feed_slug' => [
                'type' => 'meta',
            ],
            'is_active' => [
                'type' => 'toggle',
                'label' => __('Enable integration'),
                'default' => true,
            ],
            'user_id' => [
                'label' => __('Private integration'),
                'description' => __('Hide this integration from other users'),
                'trueValue' => auth()->id(),
                'falseValue' => null,
                'type' => 'toggle',
                'rules' => [
                    [
                        'required' => [
                            'remote_type',
                            Integration::INTEGRATION_TYPES_WITH_MANDATORY_USER,
                        ],
                    ],
                ],
            ],
            'imap_path' => [
                'type' => 'text',
                'label' => __('IMAP path'),
                'conditions' => [
                    ['remote_type', [Integration::TYPE_CV_KESKUS, Integration::TYPE_CV_MARKET, Integration::TYPE_CV_MARKET_LT, Integration::TYPE_IMAP_MESSAGES]],
                ],
                'description' => __('e.g. {imap.gmail.com:993/imap/ssl}INBOX or {outlook.office365.com:993/imap/ssl}INBOX'),
            ],
            'feed_url' => [
                'type' => 'text',
                'label' => __('Feed URL'),
                'conditions' => [
                    ['remote_type', Integration::TYPE_CV_KESKUS_XML],
                ],
            ],
            'username' => [
                'type' => 'text',
                'label' => __('Username'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_CV_KESKUS_XML,
                            Integration::TYPE_IMAP_MESSAGES,
                            Integration::TYPE_CV_KESKUS,
                            Integration::TYPE_CV_MARKET,
                            Integration::TYPE_CV_MARKET_LT,
                            Integration::TYPE_CV_LIBRARY,
                            Integration::TYPE_BAMBOOHR,
                            Integration::TYPE_CV_DOT_LT,
                            Integration::TYPE_TOTALJOBS,
                            Integration::TYPE_RETAILCHOICE,
                            Integration::TYPE_VISMA_HOP,
                        ],
                    ],
                ],
            ],
            'email' => [
                'type' => 'text',
                'label' => __('E-mail'),
                'description' => __('E-mail address of the Indeed account used to manage this integration'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_INDEED,
                        ],
                    ],
                ],
                'rules' => [
                    'email',
                    [
                        'required' => [
                            'remote_type',
                            [
                                Integration::TYPE_INDEED,
                            ],
                        ],
                    ],
                ],
            ],
            'notification_email' => [
                'type' => 'text',
                'label' => __('Notification email'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_EMAIL_NOTIFICATION,
                        ],
                    ],
                ],
            ],
            'password' => [
                'type' => 'text',
                'label' => __('Password'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_CV_KESKUS_XML,
                            Integration::TYPE_IMAP_MESSAGES,
                            Integration::TYPE_CV_KESKUS,
                            Integration::TYPE_CV_MARKET,
                            Integration::TYPE_CV_MARKET_LT,
                            Integration::TYPE_CV_LIBRARY,
                            Integration::TYPE_CV_DOT_LT,
                            Integration::TYPE_TOTALJOBS,
                            Integration::TYPE_RETAILCHOICE,
                            Integration::TYPE_VISMA_HOP,
                        ],
                    ],
                ],
            ],
            'api_key' => [
                'type' => 'text',
                'label' => __('API key'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_CV_EE,
                            Integration::TYPE_CV_LV,
                            Integration::TYPE_CV_LT,
                            Integration::TYPE_CV_BANKAS,
                            Integration::TYPE_HIBOB,
                            Integration::TYPE_BAMBOOHR,
                            Integration::TYPE_JOTFORM,
                            Integration::TYPE_CV_DOT_LT,
                            Integration::TYPE_APPRENTICESHIPS_GOV_UK,
                            Integration::TYPE_VISMA_HOP,
                        ],
                    ],
                ],
            ],
            'indeed_api_info' => [
                'type' => 'static',
                'persist' => false,
                'content' => self::getIndeedApiInfoContent(),
                'conditions' => [
                    ['remote_type', [Integration::TYPE_INDEED]],
                ],
            ],
            'totaljobs_api_info' => [
                'type' => 'static',
                'persist' => false,
                'content' => self::getTotaljobsApiInfoContent(),
                'conditions' => [
                    ['remote_type', [Integration::TYPE_TOTALJOBS]],
                ],
            ],
            'retailchoice_api_info' => [
                'type' => 'static',
                'persist' => false,
                'content' => self::getRetailChoiceApiInfoContent(),
                'conditions' => [
                    ['remote_type', [Integration::TYPE_RETAILCHOICE]],
                ],
            ],
            'client_id' => [
                'type' => 'text',
                'label' => __('Client ID'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_INDEED,
                            Integration::TYPE_SS_LV,
                        ],
                    ],
                ],
                'rules' => [
                    [
                        'required' => [
                            'remote_type',
                            [
                                Integration::TYPE_SS_LV,
                            ],
                        ],
                    ],
                ],
            ],
            'client_company_name' => [
                'type' => 'text',
                'label' => __('Client company name'),
                'conditions' => [['remote_type', Integration::TYPE_DUUNITORI]],
                'rules' => [['required' => ['remote_type', Integration::TYPE_DUUNITORI]]],
            ],
            'client_secret' => [
                'type' => 'text',
                'label' => __('Client Secret'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_INDEED,
                        ],
                    ],
                ],
                'rules' => [
                    [
                        'required' => [
                            'remote_type',
                            [
                                Integration::TYPE_INDEED,
                            ],
                        ],
                    ],
                ],
            ],
            'bamboohr_info' => [
                'type' => 'static',
                'persist' => false,
                'content' => self::getBambooHRInfoContent(),
                'conditions' => [
                    ['remote_type', [Integration::TYPE_BAMBOOHR]],
                ],
            ],
            'cvo_api_key_info' => [
                'type' => 'static',
                'persist' => false,
                'content' => self::getCVOApiInfoContent(),
                'conditions' => [
                    ['remote_type', [Integration::TYPE_CV_EE, Integration::TYPE_CV_LV, Integration::TYPE_CV_LT]],
                ],
            ],
            'employer_id_override' => [
                'type' => 'text',
                'label' => __('Employer ID'),
                'rules' => 'required',
                'conditions' => [
                    ['remote_type', [Integration::TYPE_CV_EE, Integration::TYPE_CV_LV, Integration::TYPE_CV_LT, Integration::TYPE_CV_DOT_LT, Integration::TYPE_PROFESSION_HU, Integration::TYPE_VISMA_HOP]],
                ],
            ],
            'cvo_id_info' => [
                'type' => 'static',
                'persist' => false,
                'content' => self::getCVOIdInfoContent(),
                'conditions' => [
                    ['remote_type', [Integration::TYPE_CV_EE, Integration::TYPE_CV_LV, Integration::TYPE_CV_LT]],
                ],
            ],
            'access_url' => [
                'type' => 'text',
                'label' => __('Access URL'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_HIBOB,
                            Integration::TYPE_SELECT_HR,
                        ],
                    ],
                ],
            ],
            'access_key' => [
                'type' => 'text',
                'label' => __('Access key'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_HIBOB,
                        ],
                    ],
                ],
            ],
            'shared_mailbox_address' => [
                'type' => 'text',
                'label' => __('Shared mailbox address'),
                'description' => __('Only fill if you want to connect to a shared mailbox that your account has access to.'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_CV_KESKUS_OFFICE,
                            Integration::TYPE_CV_MARKET_LT_OFFICE,
                            Integration::TYPE_CV_MARKET_OFFICE,
                        ],
                    ],
                ],
            ],
            'mail_folder_name' => [
                'type' => 'text',
                'label' => __('Mail folder ID'),
                'description' => __('Leave empty for default'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_CV_KESKUS_OFFICE,
                            Integration::TYPE_CV_MARKET_LT_OFFICE,
                            Integration::TYPE_CV_MARKET_OFFICE,
                            Integration::TYPE_OFFICE_MESSAGES,
                        ],
                    ],
                ],
            ],
            'flags' => [
                'type' => 'checkboxgroup',
                'label' => __('Options'),
                'json_logic_items' => $this->getFlagsJsonLogic(),
                'conditions' => $this->getFlagsFieldConditions(),
                'items' => [],
            ],
            'li_company_id' => [
                'type' => 'text',
                'label' => __('LinkedIn Company ID'),
                'rules' => 'required',
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_LINKEDIN,
                        ],
                    ],
                ],
            ],
            'organization_name' => [
                'type' => 'text',
                'label' => __('Organization name override'),
                'description' => __('Overrides the organization name from settings. Can be used if you\'re managing multiple brands'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_CV_KESKUS_XML,
                        ],
                    ],
                ],
            ],
            'form_id' => [
                'label' => __('Additional information form'),
                'type' => 'select',
                'items' => (function () {
                    $forms = Form::query()
                        ->whereNull('stage_id')
                        ->where('type', Form::TYPE_CANDIDATE)
                        ->withoutIdentityField()
                        ->orderBy('title')
                        ->get();

                    return [
                        ['value' => null, 'label' => 'No form'],
                        ...Helpers::modelsToOptions($forms, 'title'),
                    ];
                })(),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_HIBOB,
                        ],
                    ],
                ],
            ],
            'privacy_notice_html' => [
                'type' => 'trix',
                'label' => __('Privacy notice'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_PROFESSION_HU,
                        ],
                    ],
                ],
            ],
            'job_ad_fields' => [
                'type' => 'togglegroup',
                'size' => 'sm',
                'label' => __('Selected fields'),
                'description' => __('These fields will be shown in the job ad publication form.'),
                'items' => [
                    StructuredJobAd::KEY_LOCATION => __('Location'),
                    StructuredJobAd::KEY_TARGET_STAGE_ID => __('Target stage'),
                    StructuredJobAd::KEY_CONTACT_INFO => __('Contact info'),
                    StructuredJobAd::KEY_CONTACT_NAME => __('Contact name'),
                    StructuredJobAd::KEY_CONTACT_EMAIL => __('Contact email'),
                    StructuredJobAd::KEY_CONTACT_PHONE => __('Contact phone'),
                    StructuredJobAd::KEY_EMOTIONIMAGES => __('Images'),
                    StructuredJobAd::KEY_DESCRIPTION => __('Description'),
                    StructuredJobAd::KEY_REQUIREMENTS => __('Requirements'),
                    StructuredJobAd::KEY_WE_OFFER => __('We offer'),
                    StructuredJobAd::KEY_SALARY_FROM => __('Salary from'),
                    StructuredJobAd::KEY_SALARY_TO => __('Salary to'),
                    StructuredJobAd::KEY_SALARY_CURRENCY => __('Salary currency'),
                    StructuredJobAd::KEY_SALARY_PERIOD => __('Salary period'),
                    StructuredJobAd::KEY_SALARY_INFO => __('Salary info'),
                    StructuredJobAd::KEY_SS_LV_WORKING_HOURS => __('Working hours'),
                    StructuredJobAd::KEY_SCREENING_QUESTIONS => __('Screening questions'),
                    StructuredJobAd::KEY_WORK_TIMES => __('Work times'),
                    StructuredJobAd::KEY_NUMBER_OF_POSITIONS => __('Number of positions'),
                    StructuredJobAd::KEY_EMPLOYER_LOGO_LOCATION => __('Employer logo'),
                ],
                'default' => [],
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_DEFAULT_FEED,
                        ],
                    ],
                ],
            ],
            'job_ad_custom_field_configuration' => [
                'type' => 'fieldlist',
                'label' => __('Custom fields'),
                'json_logic_description' => [
                    'if' => [
                        ['==' => [['var' => 'remote_type'], Integration::TYPE_DEFAULT_FEED]], __('These fields will be added to the job ad publication form.'),
                        ['in' => [['var' => 'remote_type'], [Integration::TYPE_SELECT_HR]]], __('These fields will be added to the handover form.'),
                        'lol',
                    ],
                ],
                'parentKey' => 'job_ad_custom_field_configuration',
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_DEFAULT_FEED,
                            Integration::TYPE_SELECT_HR,
                        ],
                    ],
                ],
            ],
            'image_generation_url' => [
                'type' => 'text',
                'label' => __('Image generation URL'),
                'conditions' => [
                    [
                        'remote_type',
                        [
                            Integration::TYPE_DEFAULT_FEED,
                        ],
                    ],
                ],
            ],
        ];
    }

    public function before()
    {
        /** @var Integration|null $integration */
        $integration = $this->getEntity();
        $data = $this->data;

        if (!isset($integration?->feed_slug)) {
            $slug = Str::slug($data['name']);

            // If we added this straight into the whereRaw, it would consider the ? in the regex as a parameter
            $regex = "^$slug(-[0-9]+)?$";

            $existingIntegrations = Integration::whereRaw('feed_slug ~* ?', [$regex])->get();
            if ($existingIntegrations->isNotEmpty()) {
                $slug = $slug . '-' . ($existingIntegrations->count() + 1);
            }

            $data['feed_slug'] = $slug;
            $this->setData($data);
        }

        if (!isset($data['job_ad_fields'])) {
            $data['job_ad_fields'] = [];
            $this->setData($data);
        }

        if (!auth()->user()?->isMeetingAnalysisEnabled()) {
            $this->fail(__('Contact support to enable transcripts for your account.'));
        }
    }

    public function after()
    {
        /** @var Integration $integration */
        $integration = $this->getEntity();
        $integrationRemoteType = $integration->remote_type;

        if (app()->environment() !== 'local' && in_array($integrationRemoteType, Integration::NOT_IMPLEMENTED)) {
            Mail::mailer('postmark_smtp')->raw(
                'From ' . auth()->user()->email . ' / ' . $integrationRemoteType,
                function (\Illuminate\Mail\Message $m) {
                    $m->to('<EMAIL>');
                    $m->subject('New remote source type request');
                }
            );
        }
        if (app()->environment() !== 'local' && in_array($integrationRemoteType, Integration::JOB_BOARDS)) {
            $instance = Helpers::getCurrentWebsite();
            $user = auth()->user();
            $jobBoardName = Integration::getTypes()[$integrationRemoteType];
            (new AdminSlackAlerter)
                ->notify(<<<TEXT
$user->name ($user->email) from $instance->display_name added a new job board integration with "$jobBoardName".
TEXT
                );
            Segment::track([
                'userId' => optional(auth()->user())->email,
                'event' => 'Added integration',
                'properties' => [
                    'integration_type' => $integrationRemoteType,
                ],
            ]);
        }

        if ($integration->is_active) {
            // when user wants an active integration, let's forget
            // all our past failures
            $integration->recordSuccess();
        }

        cache()->forget("instances_using_$integrationRemoteType");

        if ($integration->isOAuthBased() && $integration->is_active) {
            return response([
                'messages' => [],
                'payload' => [
                    'updates' => [],
                    'redirect_url' => url('/oauth2/generic/login?integration_id=' . $integration->id),
                    'full_page_reload' => true,
                ],
                'status' => 'success',
            ]);
        }

        return $this->success(payload: [
            'redirect_url' => route('integrations.edit', $this->getEntity()),
        ]);
    }

    public static function getIndeedApiInfoContent(): string
    {
        $sentences = [
            __('To find/generate your Client ID and Secret:'),
            __('Sign into your Indeed account'),
            __('Once logged in, go to the "Manage app credentials" here:'),
            __('Click on the "Register a new application" button.'),
            __('Enter "Teamdash" as the application name and add a short description (e.g. "Teamdash recruitment software").'),
            __('Choose Indeed Apply as the credential type and click Save.'),
            __('Copy and paste the generated credentials into the fields below.'),
        ];

        return <<<HTML
<div class="alert alert-light text-dark-medium">
    <p>{$sentences[0]}</p>
    <ol>
        <li>{$sentences[1]}</li>
        <li>{$sentences[2]} <br/><a href="https://secure.indeed.com/account/apikeys" target="_blank" rel="noopener noreferrer">https://secure.indeed.com/account/apikeys</a></li>
        <li>{$sentences[3]}</li>
        <li>{$sentences[4]}</li>
        <li>{$sentences[5]}</li>
        <li>{$sentences[6]}</li>
    </ol>
</div>
HTML;
    }

    public static function getTotaljobsApiInfoContent(): string
    {
        $sentences = [
            __('To get your credentials for integrating with Totaljobs, please contact Totaljobs over one of the methods available at'),
        ];

        return <<<HTML
<p class="text-sm text-dark-medium">{$sentences[0]} <a href="https://www.totaljobs.com/recruiters" target="_blank" rel="noopener noreferrer">https://www.totaljobs.com/recruiters</a>.</p>
HTML;
    }

    public static function getRetailChoiceApiInfoContent(): string
    {
        $sentences = [
            __('To get your credentials for integrating with RetailChoice, please contact RetailChoice over one of the methods available at'),
        ];

        return <<<HTML
<p class="text-sm text-dark-medium">{$sentences[0]} <a href="https://www.retailchoice.com/recruiters" target="_blank" rel="noopener noreferrer">https://www.retailchoice.com/recruiters</a>.</p>
HTML;
    }

    public static function getBambooHRInfoContent(): string
    {
        $sentences = [
            __('Your username is the subdomain name you use for logging in to BambooHR.'),
            __('If your address is <i>mycompany</i>.bamboohr.com, then use <i>mycompany</i> as username.'),
            __('You can create an API key by clicking the BambooHR logo in top right corner and choosing "API Keys".'),
        ];

        return <<<HTML
<div class="alert alert-light text-dark-medium">
    <p>{$sentences[0]}</p>
    <p>{$sentences[1]}</p>
    <p>{$sentences[2]}</p>
</div>
HTML;
    }

    public static function getCVOApiInfoContent(): string
    {
        $sentences = [
            __('To find/generate your API key:'),
            __('Log in to CV online, make sure you have an administrator account'),
            __('On the top menu, click your company name'),
            __('Choose company settings'),
            __('From the left menu, choose API key'),
            __('If you already have an API key, copy it to the field above, otherwise, generate a new key'),
        ];

        return <<<HTML
<div class="alert alert-light text-dark-medium">
    <p>{$sentences[0]}</p>
    <ol>
        <li>{$sentences[1]}</li>
        <li>{$sentences[2]}</li>
        <li>{$sentences[3]}</li>
        <li>{$sentences[4]}</li>
        <li>{$sentences[5]}</li>
    </ol>
</div>
HTML;
    }

    public static function getCVOIdInfoContent(): string
    {
        $sentences = [
            __('To find your employer ID:'),
            __('After completing the API key steps, check your browser address bar'),
            __('Your URL should be like "/employer/12345/settings/..."'),
            __('Copy the number to field above'),
        ];

        return <<<HTML
<div class="alert alert-light text-dark-medium">
    <p>{$sentences[0]}</p>
    <ol>
        <li>{$sentences[1]}</li>
        <li>{$sentences[2]}</li>
        <li>{$sentences[3]}</li>
    </ol>
</div>
HTML;
    }

    private function getTypesToFlagsMap(): array
    {
        return [
            Integration::TYPE_BAMBOOHR => [
                Integration::FLAG_BAMBOO_USE_ATS,
                Integration::FLAG_HRIS_DRY_RUN,
            ],
            Integration::TYPE_HIBOB => [
                Integration::FLAG_HRIS_DRY_RUN,
            ],
            Integration::TYPE_SELECT_HR => [
                Integration::FLAG_HRIS_DRY_RUN,
            ],
            Integration::TYPE_OFFICE_CALENDAR_SCHEDULES => [
                Integration::FLAG_PLACES,
            ],
            Integration::TYPE_VISMA_HOP => [
                Integration::FLAG_HRIS_DRY_RUN,
            ],
            ...(Helpers::getCurrentWebsite()->features[Website::FEATURE_AI_MEETING_ANALYSIS] ? [
                Integration::TYPE_OFFICE_TEAMS_CALLS => [
                    Integration::FLAG_MEETING_ANALYSIS,
                    Integration::FLAG_STORE_VIDEO_RECORDINGS,
                ],
            ] : []),
        ];
    }

    private function getFlagsJsonLogic(): array
    {
        $attributes = [
            Integration::FLAG_HRIS_DRY_RUN => [
                'label' => __('Dry run (deliver debug info via email)'),
            ],
            Integration::FLAG_BAMBOO_USE_ATS => [
                'label' => __('Push candidates to BambooHR ATS'),
            ],
            Integration::FLAG_PLACES => [
                'label' => __('Enable this if you have more than 100 rooms. Will add Place.Read.All permission.'),
            ],
            Integration::FLAG_MEETING_ANALYSIS => [
                'label' => __('Enable this if you want AI-powered transcriptions and summaries from meetings. Requires permissions for your events and recordings.'),
                'cascade_to' => Integration::FLAG_STORE_VIDEO_RECORDINGS,
                'disabled' => !data_get(Helpers::getCurrentWebsite()->features, Website::FEATURE_AI_MEETING_ANALYSIS),
                'description' => !data_get(Helpers::getCurrentWebsite()->features, Website::FEATURE_AI_MEETING_ANALYSIS)
                    ? __('Contact support to enable transcripts for your account.')
                    : null,
            ],
            Integration::FLAG_STORE_VIDEO_RECORDINGS => [
                'label' => __('Enable this to store video recordings in Teamdash.'),
                'disabled_json_logic' => [
                    'or' => [
                        !data_get(Helpers::getCurrentWebsite()->features, Website::FEATURE_AI_MEETING_ANALYSIS),
                        ['!' => ['in' => [Integration::FLAG_MEETING_ANALYSIS, ['var' => 'flags']]]],
                    ],
                ],
            ],
        ];

        return [
            'if' => collect($this->getTypesToFlagsMap())
                ->flatMap(fn (array $flags, string $integrationType) => [
                    ['==' => [['var' => 'remote_type'], $integrationType]],
                    Arr::map($flags, fn ($flag) => [
                        'value' => $flag,
                        ...$attributes[$flag],
                    ]),
                ])
                ->push([])
                ->all(),
        ];
    }

    private function getFlagsFieldConditions(): array
    {
        return [
            [
                'remote_type',
                array_keys($this->getTypesToFlagsMap()),
            ],
        ];
    }
}
