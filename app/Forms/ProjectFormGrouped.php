<?php

namespace App\Forms;

use App\Classes\FeatureControls\FeatureControls;
use App\Forms\Traits\ProjectFields;
use App\Forms\Traits\StageCategoryFields;
use App\Forms\Traits\TeamFields;
use App\Helpers;
use App\Models\CrmOrganization;
use App\Models\Location;
use App\Models\Place;
use App\Models\Project;
use App\Models\ProjectFailureReason;
use App\Models\Requisition;
use App\Models\Scorecard;
use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Laraform\Laraform;

class ProjectFormGrouped extends Laraform
{
    use ProjectFields, StageCategoryFields, TeamFields;

    public $model = Project::class;

    public $component = 'project-form';

    public $endpoint = '/laraform/process';

    public $columns = [
        'element' => 12,
        'label' => 12,
        'field' => 12,
    ];

    private ?Requisition $requisition = null;

    public function schema()
    {
        $isAgency = Setting::isAgency();

        $stageCategoryFields = $this->getStageCategoryFields();

        foreach ($stageCategoryFields as $key => $value) {
            $stageCategoryFields[$key]['columns'] = [
                'element' => 4,
                'label' => 12,
                'field' => 12,
            ];
        }

        $schema = [
            'id' => [
                'type' => 'key',
            ],
            'is_template' => [
                'type' => 'meta',
                'default' => false,
            ],
            'requisition_id' => [
                'type' => 'meta',
                'persist' => false,
            ],
            'meta_1' => [
                'type' => 'group',
                'columns' => [
                    'element' => 12,
                ],
                'schema' => [
                    ...$this->getProjectNameFields(),
                    'description' => [
                        'type' => 'trix',
                        'label' => __('Description'),
                        'columns' => ['element' => 12],
                        'no_attachments' => true,
                    ],
                ],
            ],
            'meta_2' => [
                'type' => 'group',
                'columns' => [
                    'element' => 12,
                ],
                'schema' => [
                    'status_group' => [
                        'type' => 'group',
                        'columns' => [
                            'label' => 0,
                        ],
                        'schema' => [
                            'status' => [
                                'columns' => [
                                    'element' => 6,
                                ],
                                'type' => 'select',
                                'label' => __('Status') . '<span class="text-danger">*</span>',
                                'default' => Project::STATUS_IN_PROGRESS,
                                'conditions' => [
                                    ['is_template', false],
                                ],
                                'items' => array_map(
                                    fn ($k, $v) => ['value' => $k, 'label' => $v],
                                    array_keys(Project::getStatuses()),
                                    Project::getStatuses()
                                ),
                            ],
                            'project_failure_reason_id' => [
                                'type' => 'select',
                                'rules' => Setting::get(Setting::KEY_REQUIRE_PROJECT_FAILURE_REASONS) ? 'required' : [],
                                'items' => Setting::get(Setting::KEY_REQUIRE_PROJECT_FAILURE_REASONS)
                                    ? ProjectFailureReason::getFormItemsGrouped()
                                    : [
                                        ['value' => null, 'label' => __('Unspecified')],
                                        ...ProjectFailureReason::getFormItemsGrouped(),
                                    ],
                                'label' => __('Failure reason'),
                                'conditions' => [
                                    ['meta_2.status_group.status', Project::STATUS_FAILED],
                                ],
                                'columns' => [
                                    'element' => 6,
                                ],
                            ],
                        ],
                    ],
                    'project_dates' => [
                        'type' => 'group',
                        'schema' => [
                            'start_date' => [
                                'type' => 'date',
                                'rules' => 'nullable',
                                'label' => __('Start date'),
                                'default' => Carbon::now()->format('Y-m-d'),
                                'columns' => [
                                    'element' => 6,
                                ],
                                'conditions' => [
                                    ['is_template', false],
                                ],
                            ],
                            'deadline_at' => [
                                'type' => 'date',
                                'rules' => 'nullable',
                                'label' => __('Deadline'),
                                'conditions' => [
                                    ['meta_2.is_perpetual', 0],
                                    ['is_template', false],
                                ],
                                'columns' => [
                                    'element' => 6,
                                ],
                                'class' => 'rightMost',
                            ],
                        ],
                    ],
                    'end_date' => [
                        'type' => 'date',
                        'rules' => 'nullable',
                        'label' => __('End date'),
                        'conditions' => [
                            ['meta_2.status_group.status', Project::FINISHED_STATUSES],
                        ],
                        'columns' => [
                            'element' => 6,
                        ],
                        'format' => 'Y-m-d',
                        'default' => Carbon::now()->format('Y-m-d'),
                    ],
                    'warranty_until' => [
                        'type' => 'date',
                        'rules' => 'nullable',
                        'label' => __('Warranty until'),
                        'conditions' => [
                            ['meta_2.status_group.status', Project::STATUS_FINISHED],
                        ],
                        'tooltip' => __('Enter warranty date if applicable'),
                        'columns' => [
                            'element' => 6,
                        ],
                    ],
                    'is_perpetual' => [
                        'type' => 'toggle',
                        'label' => '',
                        'text' => __('Continuous project (always accepting candidates)'),
                        'default' => false,
                        'text_as_label' => true,
                        'columns' => [
                            'element' => 12,
                            'field' => 12,
                            'label' => 0,
                        ],
                    ],
                    'perpetual_gdpr_consent_length' => [
                        'type' => 'select',
                        'label' => __('GDPR consent length') . '<span class="text-danger">*</span>',
                        'default' => Project::PERPETUAL_GDPR_CONSENT_LENGTH_6M,
                        'items' => array_map(
                            fn ($k, $v) => ['value' => $k, 'label' => $v],
                            array_keys(Project::getPerpetualGdprConsentLengths()),
                            Project::getPerpetualGdprConsentLengths()
                        ),
                        'conditions' => [
                            ['meta_2.is_perpetual', 1],
                        ],
                        'rules' => ['required'],
                        'columns' => [
                            'element' => 12,
                            'field' => 5,
                            'label' => 12,
                        ],
                    ],
                ],
            ],
            'member_group' => [
                'type' => 'group',
                'schema' => [
                    ...$this->getTeamFields(),
                    'project_manager_id' => [
                        'type' => 'select',
                        'items' => User::orderBy('name')
                            ->active()
                            ->get()->map(function ($user) {
                                return [
                                    'value' => $user->id,
                                    'label' => $user->name,
                                ];
                            }),
                        'label' => __('Project manager') . '<span class="text-danger">*</span>',
                        'placeholder' => __('Choose project manager'),
                        'rules' => 'required',
                        'trackBy' => 'name',
                        'default' => auth()->id(),
                        'conditions' => [
                            ['is_template', false],
                        ],
                        'persist' => false, // We handle persisting ourselves in after()
                    ],
                    'users' => [
                        'type' => 'tags',
                        'search' => true,
                        'label' => __('Members'),
                        'items' => User::query()
                            ->active()
                            ->orderBy('name')
                            ->get()
                            ->map(function ($user) {
                                return [
                                    'value' => $user->id,
                                    'label' => $user->name,
                                ];
                            })->values()->toArray(),
                        'columns' => [
                            'element' => 12,
                        ],
                        'conditions' => [
                            ['is_template', false],
                        ],
                        'persist' => false, // We handle persisting ourselves in after()
                    ],
                    'accessible_only_members' => [
                        'type' => 'toggle',
                        'text' => __('Project visible only for members'),
                        'default' => Setting::get(Setting::KEY_PROJECTS_PRIVATE_BY_DEFAULT) ? 1 : 0,
                        'trueValue' => 1,
                        'falseValue' => 0,
                        'text_as_label' => true,
                        'columns' => [
                            'element' => 12,
                            'field' => 12,
                            'label' => 0,
                        ],
                        'conditions' => [
                            ['is_template', false],
                        ],
                    ],
                ],
            ],
            ...($isAgency ? [
                'client_group' => [
                    'type' => 'group',
                    'schema' => [
                        'crm_organization_id' => [
                            'type' => 'select',
                            'items' => CrmOrganization::orderBy('name')
                                ->get()->map(function ($client) {
                                    return [
                                        'value' => $client->id,
                                        'label' => $client->name,
                                    ];
                                })->prepend(['label' => $isAgency ? __('Add new client') : __('Add new manager'), 'value' => 'new'])
                                ->prepend(['label' => $isAgency ? __('Choose client') : __('Choose manager'), 'value' => null]),
                            'label' => $isAgency ? __('Client') : __('Manager'),
                            'trackBy' => 'name',
                        ],
                        'crm_office_id' => [
                            'type' => 'select',
                            'label' => __('Office'),
                            'json_logic_items' => [
                                'if' => CrmOrganization::query()->with(['offices'])->get()
                                    ->map(function (CrmOrganization $organization) {
                                        return [
                                            ['==' => [['var' => 'crm_organization_id'], $organization->id]],
                                            [['value' => null, 'label' => '-'], ...Helpers::modelsToOptions($organization->offices)],
                                        ];
                                    })->flatten(1)->toArray(),
                            ],
                            'items' => [],
                            'conditions' => [
                                ['client_group.crm_organization_id', '!=', null],
                                ['client_group.crm_organization_id', '!=', 'new'],
                            ],
                        ],
                        'new_client' => [
                            'type' => 'object',
                            // 'class' => 'bg-gray-light border border-gray mb-2 pl-3 pr-3 pt-3 rounded',
                            'conditions' => [
                                ['client_group.crm_organization_id', 'new'],
                            ],
                            'columns' => [
                                'element' => 12,
                                'label' => 0,
                                'field' => 12,
                            ],
                            'persist' => false,
                            'schema' => [
                                'name' => [
                                    'type' => 'text',
                                    'rules' => 'required',
                                    'label' => $isAgency ? __('Client name') : __('Manager name'),
                                ],
                                'client_manager_id' => [
                                    'type' => 'select',
                                    'label' => __('Managing recruiter'),
                                    'items' => User::orderBy('name')
                                        ->active()
                                        ->get()
                                        ->map(function (User $user) {
                                            return [
                                                'value' => $user->id,
                                                'label' => $user->name,
                                            ];
                                        }),
                                ],
                            ],
                        ],
                    ],
                ],
            ] : []),
            'meta_3' => [
                'type' => 'group',
                'schema' => [
                    //                    'monthly_salary' => [
                    //                        'type' => 'text',
                    //                        'label' => __('Salary range'),
                    //                    ],
                    //                    'landing_page' => [
                    //                        'type' => 'text',
                    //                        'rules' => 'url|nullable',
                    //                        'label' => __('Landing page'),
                    //                    ],
                    'scorecards' => [
                        'type' => 'tags',
                        'label' => __('Scorecards') . Helpers::getUpgradeBadge('scorecards'),
                        'items' => Scorecard::orderBy('name')
                            ->get()->map(function ($scorecard) {
                                return [
                                    'value' => $scorecard->id,
                                    'label' => $scorecard->name,
                                ];
                            }),
                        'disabled' => !FeatureControls::get()->scorecards->view,
                    ],
                    ...(Setting::get(Setting::KEY_USE_PROJECT_LOCATIONS_FEATURE) ?
                        [
                            'locations' => [
                                'type' => 'tags',
                                'label' => __('Locations'),
                                'items' => [
                                    ...Helpers::modelsToOptions(Location::orderBy('name')->get()),
                                    ['label' => __('Add new location'), 'value' => 'new'],
                                ],
                            ],
                            'new_location' => [
                                'type' => 'object',
                                'persist' => false,
                                'conditions' => [
                                    ['meta_3.locations', ['new']],
                                ],
                                'columns' => [
                                    'element' => 12,
                                    'label' => 0,
                                    'field' => 12,
                                ],
                                'schema' => [
                                    'name' => [
                                        'type' => 'text',
                                        'rules' => 'required',
                                        'label' => __('Location name'),
                                        'columns' => [
                                            'element' => 6,
                                        ],
                                    ],
                                    'place' => [
                                        'type' => 'place',
                                        'label' => __('Location address'),
                                        'columns' => [
                                            'element' => 6,
                                        ],
                                    ],
                                ],
                            ],
                        ] : []),
                ],
            ],
            'custom_fields' => [
                'type' => 'object',
                'columns' => [
                    'element' => 12,
                    'label' => 0,
                    'field' => 12,
                ],
                'schema' => Setting::getProjectCustomFields()->toArray(),
            ],
            'stages' => [
                'type' => 'list',
                'label' => __('Stages'),
                'class' => 'stage-editor',
                'sort' => true,
                'orderBy' => 'sort_order',
                'storeOrder' => 'sort_order',
                'default' => Setting::get(Setting::KEY_DEFAULT_STAGES),
                'columns' => [
                    'element' => 12,
                    'field' => 12,
                ],
                'object' => [
                    'columns' => [
                        'element' => 8,
                        'label' => 0,
                        'field' => 12,
                    ],
                    'schema' => [
                        'id' => [
                            'type' => 'key',
                        ],
                        'name' => [
                            'label' => __('Stage name'),
                            'type' => 'text',
                            'rules' => 'required',
                            'columns' => [
                                'element' => 6,
                                'label' => 12,
                                'field' => 12,
                            ],
                        ],
                        ...$stageCategoryFields,
                        'sort_order' => [
                            'type' => 'meta',
                        ],
                    ],
                ],
            ],
        ];

        return $schema;
    }

    public function before()
    {
        if (data_get($this->data, 'requisition_id')) {
            $this->requisition = Requisition::findOrFail($this->data['requisition_id']);
        }

        $data = $this->data;
        $currentUserId = auth()->id();

        $projectAccessibleOnlyMembers = data_get($data, 'accessible_only_members', 0);

        if ($projectAccessibleOnlyMembers == 1 && $data['project_manager_id'] != $currentUserId && !in_array($currentUserId, $data['users'] ?? [])) {
            return response()->json([
                'status' => 'fail',
                'messages' => [__('You must include yourself as manager or member for confidential projects.')],
            ]);
        }

        $request = request()->all();
        if (data_get($request, 'data.crm_organization_id') === 'new') {
            $organization = CrmOrganization::create(data_get($request, 'data.new_client'));
            $request['data']['crm_organization_id'] = $organization->id;
            $this->setData($request['data']);
            $data = $this->data;
        }

        $locations = data_get($data, 'locations', []);
        if (in_array('new', $locations)) {
            unset($locations[array_search('new', $locations)]);
            $placeData = data_get($data, 'new_location.place');
            $place = Place::create($placeData);
            $location = Location::create(['name' => data_get($data, 'new_location.name'), 'place_id' => $place->id]);
            $locations = array_merge($locations, [$location->id]);
            unset($data['new_location']);
            unset($this->schema['new_location']);
            $data['locations'] = $locations;
            $this->setElements();
            $this->setData($data);
        }

        // scopes don't matter if it's new
        if (!isset($data['id'])) {
            Project::removeAccessScope();
        } elseif (isset($data['id'])) {
            if (Project::find($data['id'])->project_manager_id == $currentUserId && $data['project_manager_id'] != $currentUserId) {
                Project::removeAccessScope();
            }
        }
    }

    public function after()
    {
        $project = Project::find($this->data['id']);

        $formUserIds = collect([...($this->data['users'] ?? []), $this->data['project_manager_id']])->unique()->filter()->values();

        // Detach removed users
        $toDetach = $project->projectUsers->pluck('user_id')->diff($formUserIds);
        if ($toDetach->isNotEmpty()) {
            $project->projectUsers()->whereIn('user_id', $toDetach)->delete();
        }

        // Attach new users
        $toAttach = $formUserIds->diff($project->projectUsers->pluck('user_id'));
        if ($toAttach->isNotEmpty()) {
            $project->projectUsers()->createMany($toAttach->map(fn ($userId) => ['user_id' => $userId]));
        }

        // Update project manager (has to be last step as we're relying on a pgsql trigger)
        if ($project->project_manager_id != $this->data['project_manager_id']) {
            $project->project_manager_id = $this->data['project_manager_id'];
            $project->save();
        }

        if ($this->requisition && $project) {
            $this->requisition->project()->associate($project)->save();
        }

        Project::restoreAccessScope();

        if (!$project) {
            return response([
                'messages' => [],
                'payload' => [
                    'updates' => [],
                    'redirect_url' => url('projects'),
                ],
                'status' => 'success',
            ]);
        }

    }

}
