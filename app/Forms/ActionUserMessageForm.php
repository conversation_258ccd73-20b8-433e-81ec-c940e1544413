<?php

namespace App\Forms;

use App\Forms\Traits\MessageHelper;
use App\Helpers;
use App\Models\Action;
use App\Models\Form;
use App\Models\Project;
use App\Models\ProjectAction;
use App\Models\ProjectRole;
use App\Models\Template;
use App\Models\User;
use Laraform\Laraform;

class ActionUserMessageForm extends Laraform
{
    use MessageHelper;

    public $model = ProjectAction::class;

    public $component = 'base-form ref="bform"';

    public $endpoint = '/laraform/process';

    public function withBaseFields(array $fields, array $headFields = []): array
    {
        $isForTemplate = false;

        $projectId = request()->input('project_id');
        if ($projectId) {
            $project = Project::find($projectId);
            $isForTemplate = $project?->is_template ?? false;
        }

        return array_merge($headFields, [
            ...($isForTemplate ? [
                'template_info' => [
                    'type' => 'static',
                    'persist' => false,
                    'content' => '<div class="alert alert-warning mb-1"><strong>' . __('You are editing an action in a template.') . '</strong> ' . __('This will not affect existing actions in projects previously created from this template.') . '</div>',
                    'conditions' => [
                        // TODO: this is bug
                        ['id', '!=', null],
                    ],
                    'columns' => [
                        'label' => 0,
                        'field' => 12,
                    ],
                ],
            ] : []),
            'id' => [
                'type' => 'key',
            ],
            'project_id' => [
                'type' => 'hidden',
            ],
            'name' => [
                'type' => 'text',
                'label' => __('Action name'),
                'description' => __('e.g. Feedback request'),
                'rules' => 'max:50|required',
            ],
            'trigger' => [
                'type' => 'select',
                'label' => __('Trigger'),
                'items' => [
                    ['value' => ProjectAction::TRIGGER_PROJECT_STATUS_CHANGE_AUTOMATIC, 'label' => __('Automatic - after project status change')],
                    //                    ...($isForTemplate ? [] : [ // Only for non-template project actions
                    //                        ['value' => Action::TRIGGER_SINGLE_AUTOMATIC, 'label' => __('Automatic - specific time in future')],
                    //                    ]),
                ],
                'default' => ProjectAction::TRIGGER_PROJECT_STATUS_CHANGE_AUTOMATIC,
                // Disabled because there is only one trigger available right now.
                'disabled' => true,
                'rules' => 'required',
            ],
            'help_status' => [
                'type' => 'static',
                'persist' => false,
                'content' => '<div class="text-sm">' . __('The action will be automatically executed after the project status has changed.') . '</div>',
                'conditions' => [
                    ['trigger', [ProjectAction::TRIGGER_PROJECT_STATUS_CHANGE_AUTOMATIC]],
                ],
            ],
            'delay' => [
                'type' => 'object',
                'label' => __('Delay'),
                'schema' => [
                    'value' => [
                        'type' => 'text',
                        'rules' => 'required|numeric|min:1',
                        'default' => 15,
                        'columns' => [
                            'label' => 0,
                            'field' => 12,
                            'element' => 2,
                        ],
                    ],
                    'unit' => [
                        'type' => 'select',
                        'items' => [
                            ['value' => 'minutes', 'label' => __('Minutes')],
                            ['value' => 'hours', 'label' => __('Hours')],
                            ['value' => 'days', 'label' => __('Days')],
                            ['value' => 'weekdays', 'label' => __('Weekdays')],
                        ],
                        'rules' => 'required',
                        'default' => 'minutes',
                        'columns' => [
                            'label' => 0,
                            'field' => 12,
                            'element' => 3,
                        ],
                    ],
                ],
                'conditions' => [
                    ['trigger', [
                        Action::TRIGGER_DELAYED_AUTOMATIC,
                        Action::TRIGGER_DELAYED_AFTER_VIDEO_RESPONSE,
                        Action::TRIGGER_DELAYED_AFTER_SUBMITTING_REFERENCES,
                        ProjectAction::TRIGGER_PROJECT_STATUS_CHANGE_AUTOMATIC,
                    ]],
                ],
            ],
            'scheduled_at' => [
                'type' => 'datetime',
                'label' => __('Time'),
                'rules' => 'required',
                'convertTimezone' => true,
                'conditions' => [
                    ['trigger', '=', Action::TRIGGER_SINGLE_AUTOMATIC],
                ],
            ],
            'filters' => [
                'label' => __('Only run if'),
                'type' => 'list',
                'rules' => 'max:1',
                'object' => [
                    // 'class' => 'bg-gray-light border border-gray mb-2 pl-3 pr-3 pt-3 rounded',
                    'columns' => [
                        'label' => 0,
                        'field' => 12,
                    ],
                    'schema' => [
                        'condition' => [
                            'type' => 'select',
                            'label' => __('Condition'),
                            'items' => [
                                ProjectAction::FILTER_CONDITION_STATUS => __('Project status is'),
                            ],
                        ],
                        'project_statuses' => [
                            'type' => 'tags',
                            'label' => __('Choose statuses'),
                            'items' => collect(Project::getStatuses())->map(function ($label, $value) {
                                return [
                                    'value' => $value,
                                    'label' => $label,
                                ];
                            })->values()->toArray(),
                            'conditions' => [
                                ['filters.*.condition', '=', ProjectAction::FILTER_CONDITION_STATUS],
                            ],
                        ],
                    ],
                ],
                'default' => [
                    [
                        'condition' => ProjectAction::FILTER_CONDITION_STATUS,
                        'project_statuses' => [Project::STATUS_FINISHED],
                    ],
                ],
                'add_btn_label' => __('Add condition'),
            ],
            'filters_condition' => [
                'label' => '',
                'type' => 'select',
                'items' => [
                    [
                        'value' => Action::FILTERS_CONDITION_AND,
                        'label' => __('Match candidates with all the conditions above.'),
                    ],
                    [
                        'value' => Action::FILTERS_CONDITION_OR,
                        'label' => __('Match candidates with at least one of the conditions above.'),
                    ],
                ],
                'default' => Action::FILTERS_CONDITION_AND,
                'json_logic' => [
                    '>' => [
                        ['var' => ['filters.length']],
                        1,
                    ],
                ],
            ],
        ], $fields);
    }

    public function schema()
    {
        $project = null;
        $projectId = request()->input('project_id');
        if ($projectId) {
            $project = Project::find($projectId);
        }

        if ($project && !$project->is_template) {
            $users = Helpers::modelsToOptions(
                User::query()->whereIn('id', [
                    ...$project->users->pluck('id'),
                    $project->project_manager_id,
                ])->get()
            );
        } else {
            $users = User::getForForm(withService: true);
        }

        $roles = ProjectRole::getForForm();

        return $this->withBaseFields(
            [
                'action_type' => [
                    'type' => 'meta',
                    'default' => ProjectAction::TYPE_USER_MESSAGE,
                ],
                'load_template' => [
                    'persist' => false,
                    'type' => 'loadtemplate',
                    'label' => __('Template'),
                    'template_type' => Template::TYPE_USER_MESSAGE,
                ],
                'data' => [
                    'type' => 'object',
                    'columns' => [
                        'label' => 0,
                        'field' => 12,
                    ],
                    'schema' => [
                        'subject' => [
                            'type' => 'text',
                            'rules' => 'required',
                            'label' => __('Subject'),
                        ],
                        'body' => [
                            'type' => 'trix',
                            'rules' => 'required',
                            'label' => __('Body'),
                            'after' => $this->getMergeTagInfo(withSurvey: false, other: ['form_url'], exclude: ['consent_renewal_url']),
                            'signature_user_id_path' => 'data.user_id',
                            'clickable_merge_tags' => true,
                            'writing_assistant' => [
                                'context' => 'The user is writing an email message to other users.',
                                'specific_prompts' => [
                                    [
                                        'prompt' => __('Ask user for feedback about hiring process'),
                                        'name' => 'hiring_process_feedback',
                                        'needs_content' => false,
                                    ],
                                ],
                            ],
                        ],
                        ...$this->getFormIdField('data.body', formTypes: [Form::TYPE_USER]),
                        'user_id' => [
                            'type' => 'select',
                            'label' => __('Send as'),
                            'rules' => 'required',
                            'description' => __('This person is marked as the sender.'),
                            'items' => Helpers::modelsToOptions($this->getAvailableSendAsUsers()),
                            'default' => auth()->id(),
                        ],
                        'send_to_type' => [
                            'type' => 'radiogroup',
                            'label' => __('Send to'),
                            'items' => [
                                Action::SEND_TO_TYPE_ALL_USERS => __('All project users'),
                                Action::SEND_TO_TYPE_SELECT_USERS => __('Select users'),
                                ...($roles->isEmpty() ? [] : [Action::SEND_TO_TYPE_SELECT_ROLES => __('Select roles')]),
                            ],
                            'rules' => 'required',
                        ],
                        'user_ids' => [
                            'type' => 'tags',
                            'label' => __('Users to ping'),
                            'items' => $users,
                            'search' => true,
                            'description' => $project && $project->is_template ? __('These users will be added to every project you create from this template.') : null,
                            'conditions' => [
                                ['data.send_to_type', '=', Action::SEND_TO_TYPE_SELECT_USERS],
                            ],
                            'rules' => 'required',
                        ],
                        ...($roles->isEmpty() ? [] : ['role_ids' => [
                            'type' => 'tags',
                            'rules' => '',
                            'label' => __('User roles to ping'),
                            'items' => $roles,
                            'search' => true,
                            'conditions' => [
                                ['data.send_to_type', '=', Action::SEND_TO_TYPE_SELECT_ROLES],
                            ],
                            'rules' => 'required',
                        ]]),
                    ],
                ],
                'save_as_template' => [
                    'persist' => false,
                    'type' => 'toggle',
                    'label' => __('Save as template'),
                    'description' => __('You can load saved message templates later. Attachments are not saved.'),
                ],
                'template_name' => [
                    'type' => 'text',
                    'persist' => false,
                    'label' => __('Template name'),
                    'description' => __('Template name should be descriptive eg. "Request for feedback". This is not the subject.'),
                    'conditions' => [
                        ['save_as_template', '=', true],
                    ],
                    'rules' => 'required',
                ],
            ]
        );
    }

    public function before()
    {
        /** @var array $data */
        $data = $this->data;

        if ($data['save_as_template'] === 'true') {
            $formId = data_get($data, 'data.form_id') ?? null;

            Template::create([
                'name' => $data['template_name'],
                'subject' => $data['data']['subject'],
                'body' => $data['data']['body'],
                'user_id' => auth()->id(),
                ...(isset($formId) ? ['data' => ['form_id' => $formId]] : []),
            ]);
        }
    }

    public function after()
    {
        return response([
            'messages' => [],
            'payload' => [
                'updates' => $this->updates,
                'action' => ProjectAction::find($this->updates['id'] ?? $this->data['id']),
            ],
            'status' => 'success',
        ]);
    }
}
