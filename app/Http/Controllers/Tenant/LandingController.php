<?php

namespace App\Http\Controllers\Tenant;

use App\Classes\ProjectSearch\Search;
use App\Classes\Video\HlsVideoService;
use App\DataExchange\StructuredJobs\Outputs\DefaultApiOutput;
use App\Helpers;
use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use App\Models\CustomFont;
use App\Models\File;
use App\Models\Form;
use App\Models\Integration;
use App\Models\Landing;
use App\Models\LandingTag;
use App\Models\Project;
use App\Models\Setting;
use App\Models\Stage;
use App\Models\StructuredJobAd;
use App\Models\Video;
use App\Services\LandingStats\LandingStats;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Intervention\Image\Facades\Image;

class LandingController extends Controller
{
    public function __construct(readonly private HlsVideoService $hlsVideoService) {}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index2(Request $request)
    {
        $this->authorize('viewAny', Landing::class);
        $query = Landing::query()->with(['structuredAds', 'tags']);

        if (!$request->has('archived')) {
            $query->where('status', '!=', Landing::STATUS_ARCHIVED);
        }

        $results = $query->orderByDesc('id')->paginate();
        \JavaScript::put('landings', $results);
        \JavaScript::put('teams_options', Helpers::modelsToOptions(auth()->user()->getAccessibleTeams()));
        \JavaScript::put('landing_tags_options', Helpers::modelsToOptions(LandingTag::all()));

        if ($landingId = $request->get('publish_landing_id')) {
            $selectedLanding = Landing::find($landingId);
            $selectedLanding->load('stage.project');
            \JavaScript::put('selected_landing', $selectedLanding->append('display_name'));
        }

        return view('landings.index', [
            'landings' => $results,
            'pageTitle' => __('Job ads'),
        ]);
    }

    public function index(Request $request)
    {
        return Inertia::render('landing_v2/pages/Index', [
            'landings' => function () {
                $landings = Landing::query()->with([
                    'structuredAds.image',
                    // All this is needed to avoid N+1 problems caused by all kinds of
                    // appends (landings.permalink, getLinks)
                    'structuredAds.integrations.pivot.structuredJobAd.image',
                    'structuredAds.integrations.pivot.structuredJobAd.project',
                    'structuredAds.integrations.pivot.integration',
                    'structuredAds.targetStage',
                    'structuredAds.landing.stage.project',
                    'structuredAds.project',
                    'tags',
                    'team',
                    'stage.project',
                ])
                    ->when(request('name'), fn ($q, $name) => $q->nameMatches($name))
                    ->when(request('statuses', ['active', 'draft']), fn ($q, $statuses) => $q->whereIn('status', $statuses))
                    ->when(request('tag_ids'), fn ($q, $tagIds) => $q->whereHas('tags', fn ($q) => $q->whereIn('id', $tagIds)))
                    ->when(request('team_ids'), fn ($q, $teamIds) => $q->whereIn('team_id', $teamIds)->orWhereHas('stage.project', fn ($q) => $q->whereIn('team_id', $teamIds)))
                    ->when(request('activeExports', 'ANY'), fn ($q, $v) => $q->hasActiveExports($v))
                    ->when(request('landingType', 'ANY'), fn ($q, $v) => $q->whereIn('type', match ($v) {
                        'EXPORTABLE' => [Landing::TYPE_V2],
                        'LANDING' => array_diff([...Landing::CLIENT_TYPES, ...Landing::INTERNAL_TYPES], [Landing::TYPE_V2]),
                        default => [...Landing::CLIENT_TYPES, ...Landing::INTERNAL_TYPES],
                    }))
                    ->orderByDesc('is_pinned')
                    ->orderByDesc('id')
                    ->paginate();

                // Start: optimisations
                $firstApiKey = ApiKey::query()->first();

                collect($landings->items())
                    ->each(fn (Landing $l) => $l->structuredAds
                        ->each(fn ($sja) => $sja->integrations
                            ->each(function ($i) use ($firstApiKey) {
                                $i->setRelation('firstApiKey', $firstApiKey);
                                $i->pivot->integration->setRelation('firstApiKey', $firstApiKey);
                            })));

                collect($landings->items())->each(function (Landing $l) {
                    $l->structuredAds->each(function ($sja) {
                        $sja->append('links');
                    });
                });
                // End: optimisations

                return $landings;
            },
            'landingTemplate' => fn () => \App\Models\Setting::get(\App\Models\Setting::KEY_LANDING_TEMPLATE),
            'teamsOptions' => fn () => Helpers::modelsToOptions(auth()->user()->getAccessibleTeams()),
            'landingTagsOptions' => fn () => Helpers::modelsToOptions(LandingTag::all()),
            'landingTags' => fn () => LandingTag::query()->orderBy('name')->get(),
            'filters' => [
                'name' => request('name', ''),
                'statuses' => request('statuses', ['active', 'draft']),
                'tag_ids' => request('tag_ids', []),
                'team_ids' => request('team_ids', []),
                'activeExports' => request('activeExports', 'ANY'),
                'landingType' => request('landingType', 'ANY'),
            ],
            'title' => __('Job ads'),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Landing::class);
        debugbar()->disable();
        \JavaScript::put('forms', Form::query()
            ->where('status', Form::STATUS_ACTIVE)
            ->orderByDesc('id')->get());

        $template = Setting::get(Setting::KEY_LANDING_TEMPLATE);
        if ($template !== 'default') {
            \JavaScript::put('landing_data_template',
                json_decode(file_get_contents(resource_path("landings/client/templates/$template.json"))));
            \JavaScript::put('landing_template_type', $template);
            $this->putIntegrations();

            return view("landings.client.$template");
        }

        return view('landings.stylish.index');
    }

    public function getStatsForLandings(Request $request)
    {
        $landings = Landing::query()->findMany($request->get('landing_ids'));
        (new LandingStats)->addStatsToLandings($landings->all());

        return $landings->map(function (Landing $landing) {
            return [
                'id' => $landing->id,
                'impressions' => $landing->impressions,
                'submissions' => $landing->submissions,
            ];
        })->keyBy('id');
    }

    public function createV2()
    {
        $this->setLandingBuilderJsVars();

        return view('landing_v2.index');
    }

    public function createImage()
    {
        debugbar()->disable();

        return view('landings.blocks_v1.index');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Landing::class);
        $model = Landing::create($request->only([
            'data',
            'slug',
            'status',
            'type',
        ]));

        return [
            'updates' => [
                'id' => $model->id,
                'permalink' => $model->permalink,
                'is_internal' => $model->is_internal,
            ],
        ];
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Landing  $landing
     * @return \Illuminate\Http\Response
     */
    public function show(Landing $landing)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Landing  $landing
     * @return \Illuminate\Http\Response
     */
    public function edit(Landing $landing)
    {
        $this->authorize('update', $landing);
        \JavaScript::put('landing', $landing);
        \JavaScript::put('forms', Form::query()
            ->where('status', Form::STATUS_ACTIVE)
            ->orderByDesc('id')->get());

        $website = Helpers::getCurrentWebsite();
        \JavaScript::put('ai_enabled', $website->features['enable_ai']);

        $this->putIntegrations();

        $fonts = CustomFont::allWithFiles();

        if ($landing->type === Landing::TYPE_V3) {
            $this->setLandingBuilderJsVars($landing);

            return view('landing_v2.index', ['fonts' => $fonts]);
        }
        if ($landing->type !== Landing::TYPE_V1) {
            \JavaScript::put('landing_data_template',
                json_decode(file_get_contents(resource_path("landings/client/templates/$landing->type.json"))));
            \JavaScript::put('landing_template_type', $landing->type);

            return view("landings.client.$landing->type", ['fonts' => $fonts]);
        }

        return view('landings.stylish.index', ['fonts' => $fonts]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Landing  $landing
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Landing $landing)
    {
        $this->authorize('update', $landing);
        $landing->update($request->only([
            'status',
            'data',
            'slug',
            'stage_id',
            'is_internal',
        ]));

        if ($request->get('publish')) {
            $landing->update(['published_data' => $landing->data]);
            // we must clear the whole cache since the job ad might
            // be a part of any other landing's careers block
            Landing::clearSsrCache();

            dispatch(function () use ($landing) {
                $landing->generatePreview();
            });
        }

        return [
            'updates' => [
                'permalink' => $landing->permalink,
            ],
            'should_publish' => session('publishing_to_social'),
        ];
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Landing  $landing
     * @return \Illuminate\Http\Response
     */
    public function destroy(Landing $landing)
    {
        $landing->delete();

        return back();
    }

    public function uploadImage(Request $request): File
    {
        $uploadedFile = $request->file('file');

        return File::create([
            'type' => File::TYPE_LANDING_UPLOAD,
            'location' => File::store($uploadedFile),
        ]);
    }

    public function uploadVideo(Request $request): Video
    {
        $uploadedFile = $request->file('file');

        return $this->hlsVideoService
            ->createHlsVideo($uploadedFile, File::TYPE_LANDING_UPLOAD)
            ->load(['file', 'hlsPlaylist']);
    }

    public function crop(Request $request)
    {
        $url = $request->get('url');
        $coords = $request->get('coordinates', []);

        $img = Image::make($url);
        $ext = $this->getImageMimeExtension($img->mime());
        $path = sys_get_temp_dir() . DIRECTORY_SEPARATOR . Str::random(10) . '.' . $ext;
        $img->crop($coords['width'], $coords['height'], $coords['left'], $coords['top']);
        $img->save($path);

        $file = new \Symfony\Component\HttpFoundation\File\File($path);
        $uploadedFile = new UploadedFile(
            $file->getPathname(),
            $file->getFilename(),
            $file->getMimeType(),
            0,
            false
        );
        $location = File::store($uploadedFile);
        File::create([
            'type' => File::TYPE_LANDING_UPLOAD,
            'location' => $location,
        ]);

        return ['url' => Storage::url($location)];
    }

    private function getImageMimeExtension($mime): ?string
    {
        $extension = null;

        switch ($mime) {
            case 'image/jpeg':
                $extension = 'jpg';
                break;
            case 'image/png':
                $extension = 'png';
                break;
            case 'image/gif':
                $extension = 'gif';
                break;
        }

        return $extension;
    }

    public function cloneLanding(Landing $landing)
    {
        $this->authorize('update', $landing);
        /** @var Landing $copy */
        $copy = $landing->replicate(['is_pinned']);
        $copy->stage_id = null;
        $copy->save();
        if ($copy->type === Landing::TYPE_V2) {
            $publicToken = Str::random(8);
            $privateToken = Str::random(32);
            $copy->update([
                'public_token' => $publicToken,
                'private_token' => $privateToken,
            ]);
        } elseif ($copy->type === Landing::TYPE_V1) {
            $data = $copy->data;
            data_set($data, 'sections.form.url', null);
            data_set($data, 'sections.form.url_custom', null);
            data_set($data, 'sections.form.iframe_height_override', null);
            $copy->data = $data;
            $copy->status = Landing::STATUS_DRAFT;
            $copy->save();
        } elseif (in_array($copy->type, Landing::CLIENT_TYPES)) {
            $data = $copy->data;
            data_set($data, 'content.form_url_custom', null);
            data_set($data, 'content.form_url', null);
            $copy->data = $data;
            $copy->status = Landing::STATUS_DRAFT;
            $copy->save();
        } elseif ($copy->type === Landing::TYPE_V3) {
            $data = $copy->data;
            $blocks = $data['blocks'];
            foreach ($blocks as &$block) {
                if ($block['type'] === 'LandingFormBlock' || $block['type'] === 'LandingHeroFormBlock') {
                    $block['formUrl'] = null;
                }
            }
            $data['blocks'] = $blocks;
            $copy->data = $data;
            if ($copy->published_data) {
                $pData = $copy->published_data;
                $blocks = $pData['blocks'];
                foreach ($blocks as &$block) {
                    if ($block['type'] === 'LandingFormBlock' || $block['type'] === 'LandingHeroFormBlock') {
                        $block['formUrl'] = null;
                    }
                }
                $pData['blocks'] = $blocks;
                $copy->published_data = $pData;
            }
            $copy->status = Landing::STATUS_DRAFT;
            $copy->save();
        }
        $copy->updateDisplayName('Copy of ' . $copy->display_name);

        return back();
    }

    public function archive(Landing $landing)
    {
        $this->authorize('delete', $landing);
        $landing->update(['status' => Landing::STATUS_ARCHIVED]);

        return redirect()->back();
    }

    public function publish(Landing $landing)
    {
        $this->authorize('update', $landing);
        $landing->update(['status' => Landing::STATUS_ACTIVE]);
        if ($landing->type === Landing::TYPE_V3) {
            $landing->published_data = $landing->data;
            $landing->save();
        }

        return redirect()->back();
    }

    public function getSummaryStats(Landing $landing)
    {
        return (new LandingStats)->getSummaryStats($landing);
    }

    public function getRecentImages(Request $request)
    {
        $search = $request->get('search');

        $fileQuery = File::query()
            ->orderByDesc('id')
            ->whereNull('hidden_from_library_at')
            ->where('type', File::TYPE_LANDING_UPLOAD)
            ->whereNull('fileable_type');

        if ($search) {
            $fileQuery = $fileQuery->where('location', 'ilike', "%$search%");
        }

        return $fileQuery->paginate(20);
    }

    public function getRecentVideos(Request $request)
    {
        $search = $request->get('search');

        $videoQuery = Video::query()
            ->with(['file', 'hlsPlaylist'])
            ->orderByDesc('id')
            ->whereRelation('file', function (Builder $fileQuery) {
                $fileQuery
                    ->whereNull('hidden_from_library_at')
                    ->where('type', File::TYPE_LANDING_UPLOAD);
            });

        if ($search) {
            $videoQuery = $videoQuery->where('location', 'ilike', "%$search%");
        }

        return $videoQuery->paginate(20);
    }

    public function getForms()
    {
        return Form::query()
            ->where('status', Form::STATUS_ACTIVE)
            ->where('type', Form::TYPE_CANDIDATE)
            ->orderBy('title')->get();
    }

    public function loadFeedItems(string $slug)
    {
        if ($slug === 'null') {
            $except = request('except') ?? [];

            return Landing::query()
                ->where('status', Landing::STATUS_ACTIVE)
                ->where('type', Landing::TYPE_V3)
                ->whereNotIn('id', array_map('intval', $except))
                ->orderByDesc('id')
                ->get()->map(function (Landing $landing) {
                    return [
                        'url' => $landing->permalink,
                        'title' => data_get($landing, 'published_data.meta.title'),
                        'location' => '',
                        'imageUrl' => $landing->meta_image_url,
                        'customFields' => [],
                    ];
                });
        }
        $integration = Integration::where('feed_slug', $slug)->first();

        return $integration->structuredJobAds()->with(['landing', 'image', 'project'])
            ->orderByDesc('id')
            ->active()
            ->get()
            ->map(fn (StructuredJobAd $ad) => $ad->toLandingCareersItem());
    }

    protected function putIntegrations(): void
    {
        \JavaScript::put('integrations', Integration::query()
            ->where('remote_type', Integration::TYPE_DEFAULT_FEED)
            ->get()
            ->map(function (Integration $integration) {
                return [
                    'integration' => $integration,
                    'items' => (new DefaultApiOutput)->setIntegration($integration)->setPerPage(100)->getOutput(),
                ];
            })
        );
    }

    public function syncTags(Landing $landing, Request $request)
    {
        $landing->tags()->sync($request->tag_ids);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function pin(Landing $landing)
    {
        $this->authorize('update', $landing);
        $landing->update(['is_pinned' => 1]);

        return redirect()->back();
    }

    /**
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function unpin(Landing $landing)
    {
        $this->authorize('update', $landing);
        $landing->update(['is_pinned' => 0]);

        return redirect()->back();
    }

    public function downloadAsImage(Landing $landing)
    {
        $args = [];
        if ($width = request()->get('width')) {
            $args['width'] = $width;
        }
        if ($format = request()->get('format')) {
            $args['format'] = $format;
        }
        if ($targetSizeKb = request()->get('targetSizeKb')) {
            $args['targetSizeKb'] = $targetSizeKb;
        }
        if ($optimize = request()->get('optimize')) {
            $args['optimize'] = $optimize !== 'false';
        }

        [$fileName, $path] = $landing->generateImage(
            ...$args,
        );

        return response()->download($path);
    }

    public function getPreviewURL(Landing $landing)
    {
        $force = request()->has('force');
        if (!$landing->image_url || $force) {
            $landing->generatePreview();
        }

        return $landing->image_url;
    }

    private function setLandingBuilderJsVars(?Landing $landing = null): void
    {
        $website = Helpers::getCurrentWebsite();

        $projectId = request()->get('project_id');

        $projectsOptions = (new Search(collect()))
            ->setUseLoggedInUserFilters(false)
            ->getQuery()->with(['stages'])
            ->where(function (Builder $q) use ($landing) {
                $q->whereIn('status', [Project::STATUS_IN_PROGRESS, Project::STATUS_DRAFT])
                    ->orWhere('id', $landing?->id);
            })
            ->get()->map(function (Project $project) {
                return [
                    'value' => $project->id,
                    'label' => $project->position_name,
                    'children' => $project->stages->map(function (Stage $stage) {
                        return [
                            'value' => $stage->id,
                            'label' => $stage->name,
                        ];
                    }),
                ];
            });

        \JavaScript::put('context', [
            'ai_enabled' => $website->features['enable_ai'],
            'instance_name' => $website->display_name,
            'career_page_integrations' => Integration::where('remote_type', Integration::TYPE_DEFAULT_FEED)->get(),
            'career_page_project_custom_fields' => Setting::getProjectCustomFields(),
            'user' => auth()->user(),
            'projects_options' => $projectsOptions,
            'project_id' => intval($projectId),
        ]);
    }

    public function simple(Landing $landing)
    {
        $excludeBlocksQuery = request()->get('exclude_blocks');
        $excludeBlocks = $excludeBlocksQuery ? explode(',', $excludeBlocksQuery) : [];

        return $landing->toSimpleHTML($excludeBlocks);
    }

    public function socialMediaPreview(Landing $landing)
    {
        return $landing->meta_image_url ?? $this->getPreviewURL($landing);
    }

    public function history(Landing $landing)
    {
        return $landing->audits()->orderByDesc('id')->with(['user'])->get();
    }
}
