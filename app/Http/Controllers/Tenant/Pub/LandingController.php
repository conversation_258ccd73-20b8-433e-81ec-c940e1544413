<?php

namespace App\Http\Controllers\Tenant\Pub;

use App\DataExchange\StructuredJobs\Outputs\DefaultApiOutput;
use App\Helpers;
use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\Application;
use App\Models\Candidate;
use App\Models\Comment;
use App\Models\Employment;
use App\Models\File;
use App\Models\Form;
use App\Models\Integration;
use App\Models\Landing;
use App\Models\Setting;
use App\Models\Stage;
use App\Scopes\LandingAccessScope;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class LandingController extends Controller
{
    public function showFromWebsite($instance, Landing $landing)
    {
        if ($landing->url_type !== Landing::URL_TYPE_ID) {
            abort(404);
        }

        return $this->show($instance, $landing);
    }

    public function showWithUrlTokenFromWebsite($instance, $urlToken)
    {
        $landing = Landing::query()->withoutGlobalScopes([LandingAccessScope::class])->where([
            'url_token' => $urlToken,
            'url_type' => Landing::URL_TYPE_TOKEN,
        ])->firstOrFail();

        return $this->show($instance, $landing);
    }

    public function showFromInstance(Landing $landing)
    {
        if ($landing->url_type !== Landing::URL_TYPE_ID) {
            abort(404);
        }

        return $this->show(null, $landing);
    }

    public function showWithUrlTokenFromInstance($urlToken)
    {
        return $this->showWithUrlTokenFromWebsite(null, $urlToken);
    }

    public function showFromWebsiteSsr(?string $instance, Landing $landing)
    {
        debugbar()->disable();

        $this->checkAccess($landing);

        return $landing->getHtmlManager()
            ->setForImage(request()->has('for_image'))
            ->setForPreview(request()->has('for_preview'))
            ->setAllowCache(!request()->has('bypass_ssr_cache'))
            ->setForExternalApplications(request()->has('for_external_applications'))
            ->getHtml();
    }

    public function show(?string $instance, Landing $landing)
    {
        if ($landing->status === Landing::STATUS_ARCHIVED && Setting::get(Setting::KEY_LANDING_HIDE_ARCHIVED_PERMANENTLY)) {
            abort(404);
        }

        $parsedActual = parse_url(request()->url());
        $parsedExpected = parse_url($landing->permalink);

        $parsedActualPath = mb_strtolower(rtrim($parsedActual['path'] ?? '', '/'));
        $parsedExpectedPath = mb_strtolower(rtrim($parsedExpected['path'] ?? '', '/'));

        if (
            (mb_strtolower($parsedExpected['host']) !== mb_strtolower($parsedActual['host']))
            || ($parsedExpectedPath !== $parsedActualPath)
        ) {
            $parsedExpectedHost = mb_strtolower($parsedExpected['host']);
            $parsedActualHost = mb_strtolower($parsedActual['host']);
            $comparison = (mb_strtolower($parsedExpected['host']) !== mb_strtolower($parsedActual['host'])
                || mb_strtolower($parsedExpected['path'] ?? '') !== mb_strtolower($parsedActual['path'] ?? ''));

            \Log::info("parsedExpectedHost $parsedExpectedHost, parsedActualHost $parsedActualHost, parsedExpectedPath $parsedExpectedPath, parsedActualPath $parsedActualPath, comparison $comparison");
            $actualUrl = request()->url();
            \Log::info("Expected $landing->permalink, actual $actualUrl, redirecting to $landing->permalink");

            return redirect($landing->permalink);
        }

        debugbar()->disable();

        $this->checkAccess($landing);

        \JavaScript::put('is_landing', true);
        \JavaScript::put('landing', $landing);

        if (in_array($landing->type, Landing::CLIENT_TYPES)) {
            \JavaScript::put('landing_data_template',
                json_decode(file_get_contents(resource_path("landings/client/templates/$landing->type.json"))));
            \JavaScript::put('landing_template_type', $landing->type);

            \JavaScript::put('integrations', Integration::query()
                ->where('remote_type', Integration::TYPE_DEFAULT_FEED)
                ->get()
                ->map(function (Integration $integration) {
                    return [
                        'integration' => $integration,
                        'items' => (new DefaultApiOutput)->setIntegration($integration)->setPerPage(100)->getOutput(),
                    ];
                })
            );

            if (\request()->has('printing')) {
                \JavaScript::put('full', true);
            }

            return view("landings.client.$landing->type", [
                'landing' => $landing,
            ]);
        } elseif ($landing->type === Landing::TYPE_V3) {
            return $this->showFromWebsiteSsr($instance, $landing);
        }

        return view('landings.stylish.index', [
            'landing' => $landing,
        ]);
    }

    public function cvlProxy(string $instance, Landing $landing, string $slug, Request $request)
    {
        $res = (new Client)
            ->get('https://application-tracking-partners.api.cv-library.co.uk/v1/candidate/' . $request->token)
            ->getBody()
            ->getContents();
        $data = json_decode($res, true);
        if (isset($data['error'])) {
            abort(400);
        }

        return response()->json($data);
    }

    public function cvlApply(string $instance, Landing $landing, string $slug, Request $request)
    {
        $candidate = Candidate::firstOrCreateFromEmail($request->email);
        $candidate->phone = $request->telephone;
        $candidate->name = $request->first_name . ' ' . $request->last_name;
        $coverLetter = nl2br($request->cover_letter);
        $industries = implode(', ', $request->industries);
        $commentContent = <<<HTML
<dl>
    <dt>Skills</dt>
    <dd>$request->skills</dd>
    <dt>Industries</dt>
    <dd>$industries</dd>
    <dt>Desired job title</dt>
    <dd>$request->desired_job_title</dd>
    <dt>Salary expectation</dt>
    <dd>$request->salary</dd>
    <dt>Driving license</dt>
    <dd>$request->driving_licence</dd>
    <dt>Cover letter</dt>
    <dd>$coverLetter</dd>
</dl>
HTML;
        $candidate->comments()->save(new Comment([
            'content' => $commentContent,
        ]));

        $candidate->employments()->save(new Employment(['position_title' => $request->current_job_title]));
        $candidate->save();

        /** @var Integration $cvlIntegration */
        $cvlIntegration = Integration::query()
            ->where('remote_type', Integration::TYPE_CV_LIBRARY)
            ->first();

        $client = new Client([
            'headers' => [
                'Authorization' => ['Basic ' . base64_encode(config('services.cv_library.username') . ':' . config('services.cv_library.api_key'))],
            ],
        ]);

        $location = File::store(File::getUploadedFileFromUrl(
            "https://application-tracking-partners.api.cv-library.co.uk/v1/cv/$request->token",
            $client,
            $request->cv_filename,
        ));

        $token = Arr::last(explode('/', $request->form_url));
        /** @var Form $form */
        $form = Form::query()->where('token', $token)->first();

        if ($form && $form->stage_id) {
            /** @var Stage $stage */
            $stage = Stage::find($form->stage_id);
        } else {
            $stage = null;
        }

        $candidate->files()->save(new File([
            'type' => File::TYPE_CV,
            'project_id' => optional($stage)->project_id,
            'location' => $location,
        ]));

        if ($stage) {
            $stage->addCandidate($candidate, function (Application $application) use ($cvlIntegration) {
                Activity::log(Activity::ADDED_TO_STAGE_FROM_EXTERNAL, [
                    $application,
                    $cvlIntegration,
                ]);
            });
        }

        try {
            $client->get('https://application-tracking-partners.api.cv-library.co.uk/v1/application-completed/' . $request->token);
        } catch (GuzzleException $e) {
            Helpers::toLogOrSentry($e);
        }

        return [
            'status' => 'OK',
        ];
    }

    private function checkAccess(Landing $landing): void
    {
        $user = auth()->user();
        if ($landing->status === Landing::STATUS_DRAFT && !$user) {
            if (!$this->passesPageCaptureAuthCode($landing)) {
                abort(404);
            }
        }

        if ($landing->is_internal && !$user) {
            $ip = request()?->ip();

            if (Helpers::passesIpGate($ip, Setting::get(Setting::KEY_INTERNAL_NETWORK_ADDR_INFO))) {
                return;
            }

            if ($this->passesPageCaptureAuthCode($landing)) {
                return;
            }

            abort(response()->view('errors.401-landing', [], 401));
        }
    }

    public function passesPageCaptureAuthCode(Landing $landing): bool
    {
        $authCode = request()->get('auth');
        if (!$authCode) {
            return false;
        }

        $website = Helpers::getCurrentWebsiteIfSet();
        ['landing_id' => $landingId, 'tenant_id' => $tenantId, 'expires_at' => $expiresAt] = decrypt($authCode);
        \Log::info("Received auth code for tenant $tenantId, landing $landingId, expires at $expiresAt");

        if ($landingId !== $landing->id || $tenantId !== $website->uuid || $expiresAt < now()) {
            return false;
        }

        return true;
    }
}
