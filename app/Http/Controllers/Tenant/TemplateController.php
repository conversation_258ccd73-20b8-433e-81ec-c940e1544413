<?php

namespace App\Http\Controllers\Tenant;

use App\Classes\FeatureControls\FeatureControls;
use App\Forms\TemplateForm;
use App\Helpers;
use App\Http\Controllers\Tenant\Settings\RegistryBaseController;
use App\Models\Template;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TemplateController extends RegistryBaseController
{
    protected string $form = TemplateForm::class;

    /** @var class-string<Model> */
    protected string $model = Template::class;

    public function get(Request $request)
    {
        $this->authorize('viewAny', Template::class);
        $query = Template::query();
        if ($type = $request->get('type')) {
            $query->where('template_type', $type);
        }
        $query->where(function (Builder $q) {
            $q->where('user_id', auth()->id())->orWhereNull('user_id');
        });

        $query->with(['team']);

        return $query->orderBy('name')->get();
    }

    public function index()
    {
        $request = request();
        $this->authorize('viewAny', Template::class);

        $templateTypes = $request->get('template_types', []);
        $teamIds = $request->get('team_ids', []);

        return Inertia::render('templates/pages/TemplatesIndex', [
            'templates' => Template::query()
                ->when($templateTypes, function (Builder $q) use ($templateTypes) {
                    $q->whereIn('template_type', $templateTypes);
                })
                ->when(!FeatureControls::get()->templates->update, function (Builder $q) {
                    $q->where(function (Builder $userTemplates) {
                        $userTemplates->where('user_id', auth()->id());
                    });
                })
                ->when($teamIds, function (Builder $q) use ($teamIds) {
                    $q->whereIn('team_id', $teamIds);
                })
                ->with(['user', 'team'])
                ->orderByDesc('id')
                ->get(),
            'templateTypes' => Helpers::assocToOptions(Template::getTypes()),
            'teamsOptions' => fn () => Helpers::modelsToOptions(auth()->user()->getAccessibleTeams()->sortBy('name')),
            'filters' => [
                'template_types' => $templateTypes,
                'team_ids' => $teamIds,
            ],
            'title' => __('Message templates'),
        ]);
    }

    public function show(Template $template)
    {
        $this->authorize('view', $template);

        return redirect()->route('templates.index');
    }

    public function edit($id)
    {
        $template = $this->resolveModel($id);

        $this->authorize('update', $template);

        $template->append([
            'message_body',
            'event_set_body',
            'invite_body',
            'video_interview_body',
            'video_message_body',
            'consent_message_body',
            'reference_body',
            'sms_body',
            'user_message_body',
        ]);

        return Inertia::render($this->detailView, [
            'model' => $template,
            'form' => $this->getForm($template),
            'title' => $this->getEditTitle(),
        ]);
    }

    protected function getEditTitle(): string
    {
        return __('Edit message template');
    }

    protected function getCreateTitle(): string
    {
        return __('Create message template');
    }

    protected function getMainTitle(): string
    {
        return __('Message templates');
    }
}
