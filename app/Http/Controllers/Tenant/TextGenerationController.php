<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\Candidate;
use App\Models\Project;
use App\Models\Setting;
use App\Services\AI\HasOpenAIClient;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TextGenerationController extends Controller
{
    use HasOpenAIClient;

    public function generate(Request $request)
    {
        // gather data: project, candidates, brand info

        $currentEditorContents = $request->input('data.current_editor_content');
        $assistanceContext = $request->input('data.assistance_context', 'The user is writing a message to a candidate.');
        $mergeTagInfo = $request->input('data.merge_tag_info');
        $format = $request->input('data.format');
        /** @var Project $project */
        $project = Project::query()->find($request->input('ctx.project_id'));

        $candidate = Candidate::query()->find($request->input('data.ids.candidate_id'));

        $promptParts = [
            'You are a writing assistant in an ATS product.',
            $assistanceContext,
        ];

        if ($mergeTagInfo) {
            $promptParts[] = "Here is the information about merge tags: $mergeTagInfo";
            $promptParts[] = 'There are no other merge tags available.';
            $promptParts[] = 'Do not wrap url merge tags in a HTML anchor tag.';
            $promptParts[] = 'Do not add a signature.';
            $promptParts[] = 'Do not add a sender name.';
        }

        if ($project) {
            $promptParts[] = 'Information about the recruitment project:';
            $promptParts[] = "Position: $project->position_name";
            $promptParts[] = "Candidate count: $project->candidates_count";
            if ($project->description) {
                $promptParts[] = 'Here is the position description:';
                $promptParts[] = @\Soundasleep\Html2Text::convert($project->description);
                $promptParts[] = '--- end of position description ---';
            }
        }

        if ($candidate) {
            $promptParts[] = 'This is being written about a single candidate';
            $promptParts[] = "The candidate name is: $candidate->name";

            if ($candidate->lastCv && $candidate->lastCv->contents) {
                $promptParts[] = 'For context, here are the candidate CV contents:';
                $promptParts[] = Str::limit($candidate->lastCv->contents, 4000);
                $promptParts[] = '--- end of candidate CV contents ---';
            }

            if ($candidate && $project) {
                /** @var Application $application */
                $application = Application::where('candidate_id', $candidate->id)
                    ->where('project_id', $project->id)
                    ->first();
                if ($application) {
                    $promptParts[] = 'The candidate is at stage: '.$application->stage->name;
                    foreach ($application->screeningCriterionResponses as $response) {
                        $promptParts[] = "The candidate's screening response for '{$response->criterion->criterion}' is: $response->result ($response->details)";
                    }
                }
            }
        }

        if (Setting::get(Setting::KEY_ORGANIZATION_NAME)) {
            $promptParts[] = 'For additional context:';
            $promptParts[] = 'The user\'s company name is: ' . Setting::get(Setting::KEY_ORGANIZATION_NAME);
            if (Setting::get(Setting::KEY_BRAND_ABOUT)) {
                $promptParts[] = 'And here\'s some info about the company: ' . Str::limit(Setting::get(Setting::KEY_BRAND_ABOUT), 500);
                $promptParts[] = '--- end of company info ---';
            }
        }

        if ($currentEditorContents) {
            $promptParts[] = 'The current text editor contents are:';
            $promptParts[] = $currentEditorContents;
            $promptParts[] = '--- end of editor contents ---';
        }

        $promptParts[] = 'Write the response in same language as the prompt is, unless otherwise specified in the user prompt.';

        $expectedResultKey = match ($format) {
            'html' => 'html',
            'text' => 'text',
            default => 'text',
        };

        $promptParts[] = "The {$expectedResultKey} key of response must contain valid {$expectedResultKey}.";

        $promptText = implode("\n", $promptParts);

        $fullPrompt = <<<PROMPT
                    $promptText

                    You will return responses in the following json format:
                    {
                        "{$expectedResultKey}": ""
                    }
                    PROMPT;

        info($fullPrompt);

        $response = $this->getClient()->chat()->create([
            'model' => $this->getDefaultModel(),
            'response_format' => ['type' => 'json_object'],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $fullPrompt,
                ],
                [
                    'role' => 'user',
                    'content' => $request->input('data.prompt'),
                ],
            ],
        ]);

        info($response->toArray());

        $content = $response->choices[0]->message->content;

        $content = $this->sanitizeOpenAIJsonResponse($content);

        return $content;
    }
}
