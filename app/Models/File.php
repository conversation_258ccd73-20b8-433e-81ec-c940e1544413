<?php

namespace App\Models;

use App\Classes\PdfBox\PdfBox;
use App\Helpers;
use App\Jobs\ExtractFileDetailsWithAI;
use App\Jobs\ReadFileContents;
use App\Jobs\SaveFileEmbedding;
use App\Models\Interfaces\AiLoggableInterface;
use App\Models\Traits\AiLoggable;
use App\Scopes\FileAccessScope;
use GuzzleHttp\Psr7\Query;
use Hyn\Tenancy\Contracts\Repositories\WebsiteRepository;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use League\Flysystem\MountManager;
use League\Flysystem\UnableToCopyFile;
use Pgvector\Laravel\HasNeighbors;
use Pgvector\Laravel\Vector;
use PhpOffice\PhpWord\Element\AbstractContainer;
use PhpOffice\PhpWord\Element\Row;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\IOFactory;
use Throwable;

class File extends TenantModel implements AiLoggableInterface
{
    use AiLoggable;
    use HasFactory;
    use HasNeighbors;

    protected $guarded = [];

    protected $appends = ['url', 'display_name', 'busting_location', 'validity'];

    protected $hidden = ['contents', 'embedding'];

    protected $dates = ['start_date', 'end_date'];

    protected $casts = [
        'embedding' => Vector::class,
        'extracted_data' => 'array',
    ];

    /**
     * Controls whether file details should be extracted with AI upon creation.
     */
    public bool $skipAi = false;

    const TYPE_CV = 'cv';
    const TYPE_MESSAGE_ATTACHMENT = 'message_attachment';
    const TYPE_EVENT_SET_FILE = 'event_set_file';
    const TYPE_CONSENT_INFO = 'consent_info';
    const TYPE_FORM_SUBMISSION_FILE = 'form_submission_file';
    const TYPE_CANDIDATE_OTHER = 'candidate_other';
    const TYPE_COMMENT_ATTACHMENT = 'comment_attachment';
    const TYPE_CANDIDATE_PHOTO = 'candidate_photo';
    const TYPE_ORGANIZATION_LOGO = 'organization_logo';
    const TYPE_USER_AVATAR = 'user_avatar';
    const TYPE_PROJECT_LOG = 'project_log';
    const TYPE_LANDING_UPLOAD = 'landing_upload';
    const TYPE_LANDING_PREVIEW = 'landing_preview';
    const STRUCTURED_AD_IMAGE = 'structured_ad_image';
    const INSTANT_UPLOAD = 'instant_upload';
    const TYPE_REQUISITION_ATTACHMENT = 'requisition_attachment';
    const TYPE_EMAIL_MESSAGE = 'email_message';
    const TYPE_STRUCTURED_AD_EMOTION_IMAGE = 'structured_ad_emotion_image';
    const string TYPE_STRUCTURED_AD_BANNER_IMAGE = 'structured_ad_banner_image';

    const TYPE_VIDEO = 'video';
    const TYPE_UPLOAD_CHUNK = 'upload_chunk';

    const TYPE_MEETING_RECORDING = 'meeting_recording';

    const TYPE_HLS_PLAYLIST = 'hls_playlist';
    const TYPE_FONT = 'font';

    const HIDDEN_TYPES = [
        self::TYPE_EMAIL_MESSAGE,
    ];

    const CANDIDATE_ATTACHMENT_TYPES = [
        self::TYPE_CV,
        self::TYPE_CANDIDATE_PHOTO,
        self::TYPE_CANDIDATE_OTHER,
    ];

    const string VALIDITY_OK = 'ok';
    const string VALIDITY_EXPIRED = 'expired';
    const string VALIDITY_EXPIRING_SOON = 'expiring_soon';
    const string VALIDITY_NOT_STARTED = 'not_started';

    public static function boot()
    {
        parent::boot();

        self::created(function (File $file) {
            $jobs = [];

            if (!$file->contents) {
                \Log::info("Queueing ReadFileContents: $file->display_name");
                $jobs[] = new ReadFileContents($file);
            } elseif (!$file->embedding) {
                // This happens during replicate() where the file spawns with contents
                // See FormController@uploadFile and Submission.php:115
                $file->dispatchEmbeddingsJob();
            }

            if (!$file->skipAi) {
                \Log::info("Queueing ExtractFileDetailsWithAI: $file->display_name");
                $jobs[] = new ExtractFileDetailsWithAI($file);
            }

            Bus::chain($jobs)->dispatch();
        });
    }

    public static function booted()
    {
        static::addGlobalScope(new FileAccessScope);
    }

    public static function getSanitizedFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        if (preg_match('/\.php$/', $extension)) {
            abort(422);
        }
        $filename = $file->getClientOriginalName();
        $noExt = Str::slug(preg_replace("/\.$extension$/", '', $filename));

        $filename = "$noExt.$extension";

        return $filename;
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function getDisplayNameAttribute()
    {
        $parts = explode('/', $this->location);

        return Arr::last($parts);
    }

    public function setContents(): void
    {
        if (!$this->location || $this->contents) {
            return;
        }

        try {
            if (in_array(
                $this->type,
                [self::TYPE_VIDEO, self::TYPE_CANDIDATE_PHOTO, self::TYPE_ORGANIZATION_LOGO, self::TYPE_USER_AVATAR, self::TYPE_HLS_PLAYLIST, self::TYPE_MEETING_RECORDING])
            ) {
                // we 100% don't want to copy these files to local disk with getLocalPath.
                return;
            }

            $fullPath = $this->getLocalPath();

            if (preg_match('/\.pdf$/', $this->location)) {
                $contents = Str::limit(self::getPdfContents($fullPath), 16770000);
            } elseif (preg_match('/\.(doc|docx|odt)$/', $this->location)) {
                $contents = self::getWordContents($fullPath);
            } else {
                $contents = null;
            }

            $this->update(['contents' => $contents]);

            $this->dispatchEmbeddingsJob();
        } catch (\Exception $e) {
            info($e->getMessage());
        }
    }

    public function setPhoneNoFromContents()
    {
        $this->refresh();

        if ($this->type !== self::TYPE_CV) {
            return;
        }

        if (!$this->contents) {
            return;
        }

        /** @var Candidate $candidate */
        $candidate = $this->fileable;
        if (!$candidate || $candidate->phone) {
            return;
        }

        $phone = Helpers::getPhoneNoFromString($this->contents);

        if ($phone) {
            \Log::info("Setting phone number from file contents: candidate {$candidate->id}, $phone");
            $candidate->phone = $phone;
            $candidate->save();
        }
    }

    public function dispatchEmbeddingsJob()
    {
        if (
            $this->contents
            && in_array($this->type, [File::TYPE_CV])
            && Helpers::getCurrentWebsite()->features[Website::FEATURE_ENABLE_AI]
        ) {
            dispatch(new SaveFileEmbedding($this->id));
        }
    }

    public static function getPdfContents(string $fullPath): ?string
    {
        $contents = PdfBox::extractText($fullPath);

        return blank($contents) ? null : trim($contents);
    }

    public static function readWord($filename)
    {
        if (file_exists($filename)) {
            if (($fh = fopen($filename, 'r')) !== false) {
                $headers = fread($fh, 0xA00);

                // 1 = (ord(n)*1) ; Document has from 0 to 255 characters
                $n1 = (ord($headers[0x21C]) - 1);

                // 1 = ((ord(n)-8)*256) ; Document has from 256 to 63743 characters
                $n2 = ((ord($headers[0x21D]) - 8) * 256);

                // 1 = ((ord(n)*256)*256) ; Document has from 63744 to 16775423 characters
                $n3 = ((ord($headers[0x21E]) * 256) * 256);

                // 1 = (((ord(n)*256)*256)*256) ; Document has from 16775424 to 4294965504 characters
                $n4 = (((ord($headers[0x21F]) * 256) * 256) * 256);

                // Total length of text in the document
                $textLength = ($n1 + $n2 + $n3 + $n4);

                if ($textLength <= 0) {
                    return false;
                }

                $extracted_plaintext = fread($fh, $textLength);

                // if you want to see your paragraphs in a new line, do this
                // return nl2br($extracted_plaintext);

                return blank($extracted_plaintext) ? null : trim($extracted_plaintext);
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public static function getWordContents(string $fullPath): ?string
    {
        // See https://help.libreoffice.org/latest/en-US/text/shared/guide/start_parameters.html for more details.
        $escapedFullPath = escapeshellarg($fullPath);
        $myPid = getmypid();
        $tmpDir = 'file://' . sys_get_temp_dir() . DIRECTORY_SEPARATOR . "soffice-$myPid";

        (new Filesystem)->ensureDirectoryExists($tmpDir);

        $userInstallationArg = "-env:UserInstallation=$tmpDir";
        $extractCmd = "LANG=C.UTF-8 soffice $userInstallationArg --cat $escapedFullPath";

        $contents = (string) shell_exec($extractCmd);

        return blank($contents) ? null : trim($contents);
    }

    public static function getWordContentsOld(string $fullPath)
    {

        $res = '';
        try {
            return self::getDocumentText($fullPath);
        } catch (\Exception $e) {
        }
        if ($res === '' && preg_match('/\.doc$/i', $fullPath)) {
            $res = self::readWord($fullPath);
        }

        return $res;
    }

    public static function getDocumentText(string $filepath): string
    {
        $document = IOFactory::createReader('Word2007')
            ->load($filepath);

        $documentText = '';

        foreach ($document->getSections() as $section) {
            foreach ($section->getElements() as $element) {
                $text = self::getElementText($element);

                if (strlen($text)) {
                    // This ensures that the text from one section doesn't stickRightToTheNextSectionLikeThis
                    $documentText .= self::getElementText($element) . "\n";
                }
            }
        }

        return $documentText;
    }

    protected static function getElementText($element): string
    {
        $result = '';

        if ($element instanceof Table) {
            foreach ($element->getRows() as $subElement) {
                $result .= self::getElementText($subElement);
            }
        }

        if ($element instanceof Row) {
            foreach ($element->getCells() as $subElement) {
                $result .= self::getElementText($subElement);
            }
        }

        if ($element instanceof AbstractContainer) {
            foreach ($element->getElements() as $subElement) {
                $result .= self::getElementText($subElement);
            }
        }

        if (method_exists($element, 'getText')) {
            $elementText = $element->getText();

            // Title elements have `getText`, but it might return TextRun objects instead of strings.
            if (Helpers::canBeString($elementText)) {
                $result .= $elementText . "\n";
            } else {
                $result .= self::getElementText($elementText);
            }
        }

        return $result;
    }

    public function getUrlAttribute()
    {
        if (app()->environment('testing')) {
            return 'https://test.recruitlab.ee';
        }
        if (!$this->location) {
            return null;
        }

        return Storage::url($this->location);
    }

    public static function readDocRecursive($item)
    {
        if (method_exists($item, 'getText')) {
            $text = $item->getText();
            if (Helpers::canBeString($text)) {
                return ' ' . $text;
            } else {
                return ' ';
            }
        }
        $result = '';
        if (method_exists($item, 'getSections')) {
            $sections = $item->getSections();
            foreach ($sections as $section) {
                $result .= self::readDocRecursive($section);
            }
        }
        if (method_exists($item, 'getElements')) {
            $elements = $item->getElements();
            foreach ($elements as $element) {
                $result .= self::readDocRecursive($element);
            }
        }

        return $result;
    }

    public static function store(UploadedFile $file, bool $removeLocal = false, ?string $forceName = null): string
    {
        $filename = $forceName ?? self::getSanitizedFilename($file);
        $prefix = self::getPrefixForNewFile();
        $file->storeAs(
            $prefix,
            $filename,
        );
        $location = $prefix . '/' . $filename;

        if ($removeLocal) {
            // Remove the file from local disk
            $filesystem = new Filesystem;
            $filesystem->delete($file->getPathname());
        }

        return $location;
    }

    public static function getUploadedFileFromUrl(
        string $url,
        ?\GuzzleHttp\Client $client = null,
        ?string $filenameOverride = null
    ): UploadedFile {
        if (!$client) {
            $client = new \GuzzleHttp\Client;
        }
        $urlParts = parse_url($url);
        $pathParts = explode('/', $urlParts['path']);

        $tempDir = sys_get_temp_dir();
        $tempDir = storage_path();

        $saveTo = $tempDir . DIRECTORY_SEPARATOR . Str::random(10) . '_' . ($fileName ?? 'cv.pdf');

        // Use the 'sink' option to stream the body directly to a file
        $res = $client->get($url, ['sink' => $saveTo]);

        if ($res->hasHeader('Content-Disposition') && !$filenameOverride) {
            $headerValue = $res->getHeader('Content-Disposition');
            preg_match('/"(.*?)"/', $headerValue[0] ?? '', $m);
            $filenameOverride = isset($m[1]) ? utf8_encode($m[1]) : null;
            if (!$filenameOverride) {
                preg_match('/filename=(.*?)$/', $headerValue[0] ?? '', $m2);
                $filenameOverride = isset($m2[1]) ? utf8_encode($m2[1]) : null;
            }
        } elseif (!$filenameOverride && preg_match('/cvkeskus\.ee\/sd_application/', $url)) {
            $urlParts = parse_url($url);
            $params = Query::parse($urlParts['query']);
            if (isset($urlParts['op']) && $urlParts['op'] === 'downloadML') {
                $filenameOverride = "ML_{$params['appl_id']}.pdf";
            } else {
                $filenameOverride = "{$params['appl_id']}.pdf";
            }
        } elseif (count($urlParts) === 3 && preg_match('/(?=[\w_-]+\.\w{3,4}$).+/', $urlParts['path'],
            $m) && !$filenameOverride) {
            $filenameOverride = $m[0];
        } else {
            $info = pathinfo($saveTo);
        }

        return new UploadedFile($saveTo, $filenameOverride ?? $info['basename'], $res->getHeaderLine('Content-type'));
    }

    public function getLocalPath(): string
    {
        $mountManager = new MountManager([
            's3' => \Storage::disk('s3')->getDriver(),
            'local' => \Storage::disk('local')->getDriver(),
        ]);
        if (!$this->location) {
            throw new UnableToCopyFile;
        }
        try {
            $mountManager->copy("s3://$this->location", "local://$this->location");
        } catch (\Exception $e) {
            // this is fine.
        }

        return storage_path("app/$this->location");
    }

    public static function getPrefixForNewFile(): string
    {
        /** @var Website $website */
        $website = app(\Hyn\Tenancy\Environment::class)->tenant();
        if (!$website && request()->segments() && request()->segments()[0] === 'i') {
            $instanceName = request()->segments()[1];
            $wr = app(WebsiteRepository::class);
            $website = $wr->query()->where('uuid', 'ilike', "$instanceName.%")->first();
        }
        $parts = [
            app()->environment(),
            $website->uuid,
            Str::random(16),
        ];

        return implode('/', $parts);
    }

    public function fileable()
    {
        return $this->morphTo();
    }

    public function getBustingLocationAttribute()
    {
        return $this->location . '?bust=' . Str::random('4');
    }

    public function setBustingLocationAttribute($v)
    {
        $this->location = preg_replace('/\?bust=\w{4}/', '', $v);
    }

    public function getWhenAttribute()
    {
        return $this->created_at->setTimezone(Helpers::tz())->format('d.m.y H:i');
    }

    /**
     * Computes hash for file (downloading file if necessary) and saves hash to the the database.
     *
     * @throws Throwable
     */
    public function computeHash(): void
    {
        $this->updateOrFail([
            'hash' => md5_file($this->getLocalPath()),
        ]);
    }

    public static function guessCandidateFileTypeByFileName(string $fileName): string
    {
        return match (str($fileName)->lower()->explode('.')->last()) {
            'pdf', 'docx', 'doc', 'dot', 'rtf', 'odt', 'fodt' => File::TYPE_CV,
            'jpg', 'jpeg', 'png', 'gif' => File::TYPE_CANDIDATE_PHOTO,
            default => File::TYPE_CANDIDATE_OTHER
        };
    }

    public function scopeCandidateLastCvs(EloquentBuilder $query): void
    {
        $query->where('type', File::TYPE_CV)
            ->whereHasMorph('fileable', Candidate::class)
            ->whereNotExists(function (QueryBuilder $query) {
                $query->selectRaw(1)
                    ->from('files as inner_files')
                    ->whereNull('hidden_from_library_at')
                    ->whereColumn('inner_files.type', 'files.type')
                    ->whereColumn('inner_files.fileable_id', 'files.fileable_id')
                    ->whereColumn('inner_files.fileable_type', 'files.fileable_type')
                    ->whereColumn('inner_files.id', '>', 'files.id');
            });
    }

    public function uniqueFilename(): Attribute
    {
        return Attribute::get(function (mixed $value, array $attributes) {
            $id = $attributes['id'];
            $displayName = str($attributes['location'])->explode('/')->last();

            return "$id-$displayName";
        });
    }

    public function hasContents(): Attribute
    {
        return Attribute::get(fn () => filled($this->contents));
    }

    public function type(): Attribute
    {
        return Attribute::make(
            get: function ($value, $attributes) {
                if ($value === self::TYPE_CANDIDATE_OTHER && $attributes['file_type_id']) {
                    return "{$value}.{$attributes['file_type_id']}";
                }

                return $value;
            },
            set: function ($value) {
                $parts = explode('.', $value);

                if ($parts[0] === self::TYPE_CANDIDATE_OTHER && count($parts) === 2) {
                    $fileType = FileType::find($parts[1]);
                    if (!$fileType) {
                        throw new \Exception("File type $parts[1] not found");
                    }

                    return [
                        'type' => self::TYPE_CANDIDATE_OTHER,
                        'file_type_id' => $fileType->id,
                    ];
                }

                return $value;
            }
        );
    }

    public function fileType()
    {
        return $this->belongsTo(FileType::class);
    }

    /**
     * Returns the validity status of the file.
     * When updating this, also update the scopes below.
     *
     * @see File::VALIDITY_*
     */
    public function validity(): Attribute
    {
        $website = Helpers::getCurrentWebsite();

        if (!$website->features[Website::FEATURE_CREDENTIAL_VALIDITY]) {
            return Attribute::make(
                get: function () {
                    return null;
                }
            );
        }

        return Attribute::make(
            get: function () {
                if (!$this->start_date && !$this->end_date) {
                    return null;
                }
                $now = now();

                if ($this->start_date && $now->isBefore($this->start_date)) {
                    return self::VALIDITY_NOT_STARTED;
                }

                $validityValue = $this->fileType?->validity;
                $validityUnit = $this->fileType?->validity_unit;

                if ($this->end_date) {
                    $endDate = $this->end_date;
                } elseif ($this->start_date) {
                    if ($validityValue && $validityUnit) {
                        $endDate = $this->start_date->add($validityUnit, $validityValue);
                    } else {
                        return self::VALIDITY_OK;
                    }
                } else {
                    return null;
                }

                $notificationValue = $this->fileType->expiry_notification ?? 30;
                $notificationUnit = $this->fileType->expiry_notification_unit ?? 'day';

                $notificationDate = $endDate?->copy()->sub($notificationUnit, $notificationValue) ?? null;

                if ($now->isAfter($endDate)) {
                    return self::VALIDITY_EXPIRED;
                } elseif ($now->isAfter($notificationDate)) {
                    return self::VALIDITY_EXPIRING_SOON;
                }

                return self::VALIDITY_OK;
            }
        );
    }

    public function scopeValidityNotStarted($query)
    {
        $now = now();

        $query->whereNotNull('start_date')
            ->where('start_date', '>', $now)
            ->where(function ($query) {
                $query->whereNotNull('end_date')
                    ->orWhereNotNull('start_date');
            });
    }

    public function scopeValidityOk($query)
    {
        $now = now();

        // Exclude records where both dates are null
        $query->where(function ($q) {
            $q->whereNotNull('start_date')
                ->orWhereNotNull('end_date');
        });

        // Exclude records where start_date > now (VALIDITY_NOT_STARTED)
        $query->where(function ($q) use ($now) {
            $q->whereNull('start_date')
                ->orWhere('start_date', '<=', $now);
        });

        // Join with fileTypes
        $query->leftJoin('file_types', 'files.file_type_id', '=', 'file_types.id');

        // Build endDate and notificationDate expressions as in previous scope
        $endDateExpr = \DB::raw("
        COALESCE(
            files.end_date,
            files.start_date + (file_types.validity || ' ' || file_types.validity_unit)::INTERVAL
        )
        ");

        $query->where(function ($q) use ($now, $endDateExpr) {
            $q->whereRaw("? < $endDateExpr", [$now])
                ->orWhere(function ($q2) {
                    $q2->whereNull('files.end_date')
                        ->whereNull('file_types.validity')
                        ->whereNull('file_types.validity_unit');
                });
        });
    }

    public function scopeValidityExpiringSoon($query)
    {
        $now = now();

        // Ensure not both dates are null
        $query->where(function ($q) {
            $q->whereNotNull('start_date')
                ->orWhereNotNull('end_date');
        });

        // Join with fileTypes
        $query->leftJoin('file_types', 'files.file_type_id', '=', 'file_types.id');

        // Build endDate expression
        $endDateExpr = \DB::raw("
        COALESCE(
            files.end_date,
            files.start_date + (file_types.validity || ' ' || file_types.validity_unit)::INTERVAL
        )"
        );

        // Build notificationDate expression
        $notificationDateExpr = \DB::raw("
            $endDateExpr - (COALESCE(file_types.expiry_notification, 30) || ' ' || COALESCE(file_types.expiry_notification_unit, 'day'))::INTERVAL
        ");

        // Apply conditions
        $query->whereRaw("? >= $notificationDateExpr", [$now])
            ->whereRaw("? < $endDateExpr", [$now]);
    }

    public function scopeValidityExpired($query)
    {
        $now = now();

        // Exclude records where both dates are null
        $query->where(function ($q) {
            $q->whereNotNull('start_date')
                ->orWhereNotNull('end_date');
        });

        // Join with file_types
        $query->leftJoin('file_types', 'files.file_type_id', '=', 'file_types.id');

        // Build endDate expression
        $endDateExpr = \DB::raw("
        COALESCE(
            files.end_date,
            files.start_date + (file_types.validity || ' ' || file_types.validity_unit)::INTERVAL
        )"
        );

        // Apply condition
        $query->whereRaw("? >= $endDateExpr", [$now]);
    }

    /**
     * Check if the file is an image based on its location/filename extension.
     *
     * @return bool True if the file is an image, false otherwise
     */
    public function isImage(): bool
    {
        if (!$this->location) {
            return false;
        }

        $extension = strtolower(pathinfo($this->location, PATHINFO_EXTENSION));

        return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg']);
    }
}
