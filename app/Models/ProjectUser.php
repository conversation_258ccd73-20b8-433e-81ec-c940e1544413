<?php

namespace App\Models;

use App\Helpers;
use App\Notifications\InvitedToProject;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property-read User $user
 */
class ProjectUser extends TenantPivot
{
    public $table = 'project_user';
    public $connection = 'tenant';
    protected $primaryKey = 'id';
    public $incrementing = true;
    public $timestamps = false;

    protected static function booted()
    {
        static::created(function (ProjectUser $pivot) {
            info('Created ProjectUser');
            ActivitySubscription::query()->firstOrCreate([
                'user_id' => $pivot->user_id,
                'project_id' => $pivot->project_id,
            ]);

            // Notify all users except the one who is logged in (created the project/invited the user)
            if ($pivot->user_id != auth()->id()) self::notifyUser($pivot);
        });

        static::deleted(function (ProjectUser $pivot) {
            ActivitySubscription::query()->where([
                'user_id' => $pivot->user_id,
                'project_id' => $pivot->project_id,
            ])->delete();
        });
    }

    private static function notifyUser(ProjectUser $pivot)
    {
        if ($pivot->user->created_at->isSameHour(now())) {
            // new users don't need the notification, they get other messages
            return;
        }

        if (app()->runningInConsole() && !app()->runningUnitTests()) {
            // if it's running ie import script, we should 100% not send the messages
            // but if it's running unit tests, it's doing a request and should notify
            return;
        }

        if (!$user = auth()->user()) {
            return;
        }

        if ($pivot->project()->withoutGlobalScopes()->first()->status !== Project::STATUS_IN_PROGRESS) {
            return;
        }

        Helpers::once("project_invite_to_{$pivot->user_id}_for_{$pivot->project_id}", function () use ($pivot, $user) {
            try {
                $pivot->user->notify(new InvitedToProject(
                    $user,
                    route('projects.show', $pivot->project),
                    $pivot->project)
                );
            } catch (\Exception $e) {
                Helpers::toLogOrSentry($e);
            }
        });
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function roles()
    {
        return $this->belongsToMany(ProjectRole::class, 'project_user_roles', 'project_user_id', 'project_role_id')
            ->using(ProjectUserRole::class);
    }

    public static function pivots()
    {
        return ['is_manager', 'id'];
    }
}
