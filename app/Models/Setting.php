<?php

namespace App\Models;

use App\Classes\ConsentManagement\ConsentService;
use App\Helpers;
use App\Services\Teams\TeamGraphResolver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class Setting extends TenantModel
{
    protected $guarded = [];

    protected static $valueCasts = [
        self::KEY_CONSENT_ADMIN_USER_ID => 'int',
        self::KEY_LANDING_HOSTNAME_ID => 'int',
        self::KEY_DEFAULT_CAREERS_PAGE_LANDING_ID => 'int',
    ];

    const KEY_LANGUAGE = 'language';
    const KEY_LOCALE = 'locale';
    const KEY_TIMEZONE = 'timezone';
    const KEY_ORGANIZATION_TYPE = 'organization_type';
    const KEY_DEFAULT_STAGES = 'default_stages';
    const KEY_COMMENTS_PUBLIC_BY_DEFAULT = 'comments_public_by_default';
    const KEY_PROJECTS_PRIVATE_BY_DEFAULT = 'projects_private_by_default';
    const KEY_SHOW_ALL_PROJECT_LOGS_TO_LIMITED = 'show_all_project_logs_to_limited';
    const KEY_PROJECT_LOGS_PUBLIC_BY_DEFAULT = 'project_logs_public_by_default';
    const KEY_TEMPLATE_INVITE = 'template_invite';
    const KEY_FORM_TEMPLATE = 'form_template';
    const KEY_FORM_TEMPLATE_USERS = 'form_template_users';
    const KEY_FORM_TEMPLATE_ADDITIONAL_INFO = 'form_template_feedback';
    const KEY_FORM_TEMPLATE_SURVEY = 'form_template_survey';
    const KEY_PROJECT_CUSTOM_FIELDS = 'project_custom_fields';
    const KEY_CANDIDATE_CUSTOM_FIELDS = 'candidate_custom_fields';
    const KEY_REQUISITION_CUSTOM_FIELDS = 'requisition_custom_fields';
    const KEY_INSTANCE_EMAIL_DOMAIN = 'instance_email_domain';
    const KEY_INSTANCE_EMAIL_PROVIDER = 'instance_email_provider';
    const KEY_INSTANCE_SMTP_SETTINGS = 'instance_smtp_settings';

    const KEY_CC_LAST_EMPLOYMENT = 'cc_last_employment';
    const KEY_CC_LAST_EDUCATION = 'cc_last_education';
    const KEY_CC_TAGS = 'cc_tags';
    const KEY_CC_INDICATOR_COMMENTS = 'cc_indicator_comments';
    const KEY_CC_INDICATOR_MESSAGES = 'cc_indicator_messages';
    const KEY_CC_INDICATOR_INVITES = 'cc_indicator_invites';
    const KEY_CC_INDICATOR_VIDEO_INVITES = 'cc_indicator_video_invites';
    const KEY_CC_INDICATOR_REFERENCES = 'cc_indicator_references';
    const KEY_CC_EMAIL = 'cc_email';
    const KEY_CC_PHONE = 'cc_phone';
    const KEY_CC_OTHER_ACTIVE_CANDIDACIES = 'cc_other_active_candidacies';
    const KEY_CC_LOCATION = 'cc_location';
    const KEY_CC_SOURCE = 'cc_source';
    const KEY_CC_SHOW_INITIALS_IN_BLIND_MODE = 'cc_show_initials_in_blind_mode';
    const KEY_CC_TASKS_FROM_ALL_PROJECTS = 'cc_tasks_from_all_projects';
    const KEY_MARK_UNDERAGE_CANDIDATES = 'mark_underage_candidates';
    const KEY_UNDERAGE_AGE_UNDER = 'underage_age_under';

    const KEY_ALLOW_WITHOUT_EMAIL = 'allow_without_email';
    const KEY_ALLOW_WITHOUT_LOCATION = 'allow_without_location';
    const KEY_USE_PROJECT_LOCATIONS_FEATURE = 'use_project_locations_feature';
    const KEY_ORGANIZATION_LOGO = 'organization_logo';
    const KEY_ORGANIZATION_FAVICON = 'organization_favicon';
    const KEY_ORGANIZATION_WEBSITE_URL = 'organization_website_url';
    const KEY_ORGANIZATION_ADDRESS = 'organization_address';
    const KEY_ORGANIZATION_NAME = 'organization_name';
    const KEY_ORGANIZATION_CITY = 'organization_city';
    const KEY_ORGANIZATION_REGISTRY_CODE = 'organization_reg_code';
    const KEY_PRIVACY_POLICY_URL = 'privacy_policy_url';
    const KEY_ORGANIZATION_STREET_ADDRESS = 'organization_street_address';
    const KEY_BRAND_VIDEO_URL = 'brand_video_url';
    const KEY_BRAND_ABOUT = 'brand_about';
    const KEY_MANDATORY_2FA = 'mandatory_2fa';
    const KEY_LANDING_TEMPLATE = 'landing_template';

    const KEY_CONSENT_REQUEST_VALID_DAYS = 'consent_request_valid_days';
    const KEY_CONSENT_VALID_MONTHS = 'consent_valid_months';
    const KEY_CONSENT_DISPUTE_MONTHS = 'consent_dispute_months';
    const KEY_CONSENT_AUTOMATION_ENABLED = 'consent_automation_enabled';
    const KEY_CONSENT_AUTOMATION_SEND_RENEWALS = 'consent_automation_send_renewals';
    const KEY_CONSENT_NEXT_RENEWALS_DATE = 'consent_next_renewals_date';
    const KEY_CONSENT_NEXT_ANONYMIZATION_DATE = 'consent_next_anonymization_date';
    const KEY_CONSENT_ADMIN_USER_ID = 'consent_admin_user_id';
    const KEY_CONSENT_RENEWAL_SUBJECT = 'consent_renewal_subject';
    const KEY_CONSENT_RENEWAL_BODY = 'consent_renewal_body';
    const KEY_CONSENT_GENERAL_RENEWAL_TEXTS = 'consent_general_renewal_texts';

    const KEY_HIDE_TAGS_FROM_LIMITED_USERS = 'hide_tags_from_limited_users';
    const KEY_HIDE_FILES_FROM_LIMITED_USERS = 'hide_files_from_limited_users';

    const KEY_INHERIT_CANDIDATE_FILE_ACCESS_FROM_PROJECT = 'inherit_candidate_file_access_from_project';

    const KEY_ALWAYS_INHERIT_CANDIDATE_ACCESS_FROM_PROJECT = 'always_inherit_candidate_access_from_project';

    const KEY_ALLOW_REGULAR_USERS_CREATE_TAGS = 'allow_regular_users_create_tags';
    const KEY_REQUISITIONS_INFO_MESSAGE = 'requisitions_info_message';
    const KEY_HIDE_MESSAGES_FROM_LIMITED_USERS = 'hide_messages_from_limited_users';

    const KEY_SHOW_ALL_PROJECTS_TO_LIMITED_USERS = 'show_all_projects_to_limited_users';
    const KEY_ENABLE_SCHEDULING_FOR_LIMITED_USERS = 'enable_scheduling_for_limited_users';
    const KEY_SURVEY_QUESTION = 'survey_question';
    const KEY_PHONE_DEFAULT_COUNTRY_CODE = 'phone_default_country_code';
    const KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS = 'key_requisition_always_ask_approval_from_user_ids';
    const KEY_REQUISITION_MIN_USERS_APPROVAL = 'key_requisition_min_users_approval';
    const KEY_ENABLE_PERMANENT_DELETE = 'enable_permanent_delete';
    const KEY_REQUISITION_ORG_CHART = 'requisition_org_chart';
    const KEY_SIGNATURE_ACCENT_COLOR = 'signature_accent_color';
    const KEY_SIGNATURE_STATIC_TEXT = 'signature_static_text';
    const KEY_MS_SSO_TOKEN = 'ms_sso_token';
    const KEY_SSO_ALLOW_PASSWORD_LOGINS = 'sso_allow_password_logins';

    const KEY_SCIM_SYNC_GROUPS = 'scim_sync_groups';

    const KEY_LANDING_DOMAIN = 'landing_domain';
    const KEY_LANDING_HOSTNAME_ID = 'landing_hostname_id';
    const KEY_LANDING_HIDE_ARCHIVED_PERMANENTLY = 'landing_hide_archived_permanently';
    const KEY_DEFAULT_CAREERS_PAGE_LANDING_ID = 'default_careers_page_landing_id';
    const KEY_ICAL_WRAP_LINES = 'ical_wrap_lines';

    const KEY_LOGO_URL = 'logo_url';

    const KEY_USE_CUSTOM_STAGE_CATEGORIES = 'use_custom_stage_categories';

    const KEY_OUTGOING_EMAIL_ADDRESS_OVERRIDE_MATCH_REGEX = 'outgoing_email_address_override_match_regex';
    const KEY_OUTGOING_EMAIL_ADDRESS_OVERRIDE = 'outgoing_email_address_override';
    const KEY_ASK_FOR_DROPOUT_REASON = 'ask_for_dropout_reason';

    const KEY_MAKE_IMPORT_COMMENTS_PUBLIC = 'make_import_comments_public';

    const KEY_ENABLE_EMAIL_CLICK_TRACKING = 'enable_mail_click_tracking';

    const KEY_SEND_TENTATIVE_SCHEDULER_SLOTS = 'send_tentative_scheduler_slots';

    const KEY_INTERNAL_NETWORK_ADDR_INFO = 'internal_network_addr_info';

    const KEY_ENABLE_SEND_MAIL_AS_USER = 'enable_send_mail_as_user';

    const KEY_AI_PROVIDER = 'ai_provider';

    const KEY_ENABLE_LANDING_PREVIEW_WATERMARKS = 'enable_landing_preview_watermarks';
    const KEY_AUTOMATICALLY_ARCHIVE_LANDING_PAGES = 'automatically_archive_landing_pages';

    // This is used to maintain a list of emails that should not be recognized as candidates.
    // E.g. when an agency includes their email address on a candidate's CV without a candidate email,
    // we don't want to create a candidate with the agency email.
    // This is not visible in the instance organization settings form, but rather edited manually in the database.
    const KEY_BLOCKED_CANDIDATE_EMAILS = 'blocked_candidate_emails';

    const KEY_REQUIRE_PROJECT_FAILURE_REASONS = 'require_project_failure_reasons';

    const AI_PROVIDER_OPENAI = 'openai';
    const AI_PROVIDER_AZURE_EUR = 'azure_eur';

    const SETTING_DEFAULTS = [
        self::KEY_LANGUAGE => 'en',
        self::KEY_LOCALE => null,
        self::KEY_TIMEZONE => 'Europe/Tallinn',
        self::KEY_ORGANIZATION_TYPE => self::ORGANIZATION_TYPE_CORPORATION,
        self::KEY_DEFAULT_STAGES => [
            ['name' => 'Submissions', 'category' => Stage::CATEGORY_SUBMISSIONS],
            ['name' => 'Video interviews', 'category' => Stage::CATEGORY_INTERVIEWS],
            ['name' => 'Interview', 'category' => Stage::CATEGORY_INTERVIEWS],
            ['name' => 'Offer', 'category' => Stage::CATEGORY_OFFER],
            ['name' => 'Rejected', 'category' => Stage::CATEGORY_DROPOUT],
            ['name' => 'Hired', 'category' => Stage::CATEGORY_HIRED],
        ],
        // The following value is translated in Helpers::getTranslatedSettingDefault
        self::KEY_TEMPLATE_INVITE => <<<'RAW'
Hello, [user_name]!<br><br>
[current_user_name] has invited you to use Teamdash.<br><br>

Please set a password here: [password_url]<br><br>

With best<br>
Teamdash
RAW,
        self::KEY_FORM_TEMPLATE => [
            'title' => 'My form',
            'formFields' => [
                [
                    'label' => 'Full name',
                    'field_type' => 'text',
                    'db_field' => 'name',
                    'order' => 1,
                    'rules' => ['required'],
                ],
                [
                    'label' => 'E-mail',
                    'field_type' => 'text',
                    'db_field' => 'email',
                    'order' => 2,
                    'rules' => ['email', 'required'],
                ],
                [
                    'label' => 'Phone',
                    'field_type' => 'phone',
                    'db_field' => 'phone',
                    'order' => 3,
                    'rules' => ['required'],
                ],
                [
                    'label' => 'CV',
                    'field_type' => 'instantfile',
                    'db_field' => 'cv',
                    'order' => 4,
                ],
                [
                    'label' => 'I agree with having my data stored for 36 months.',
                    'val' => 36,
                    'order' => 5,
                    'field_type' => 'checkbox_numeric',
                    'db_field' => 'gdpr_months',
                ],
                [
                    'label' => 'When you apply, we may conduct a background check using public databases and websites and utilizing a web search engine. Your resume may be retained for a maximum period of one year. ',
                    'field_type' => 'static',
                    'order' => 6,
                    'db_field' => null,
                ],
                [
                    'label' => 'You can read about our privacy statement here.',
                    'field_type' => 'privacypolicy',
                    'order' => 7,
                    'db_field' => null,
                ],
            ],
            'submit_text' => 'Submit',
            'success_text' => 'Thank you! Your details have been sent!',
            'use_landing_theme' => true,
        ],
        self::KEY_FORM_TEMPLATE_USERS => [
            'title' => 'Feedback form',
            'type' => Form::TYPE_USER,
            'formFields' => [
                [
                    'label' => 'Thank you for participating in our recruitment process! Your feedback on the collaboration and hiring process would be highly appreciated by the recruitment team.',
                    'field_type' => 'static',
                    'order' => 1,
                ],
                [
                    'label' => 'How was your overall experience with the hiring process?',
                    'field_type' => 'numericrate',
                    'order' => 2,
                    'rules' => ['required'],
                    'min_val' => '0',
                    'max_val' => '10',
                ],
                [
                    'label' => 'How easy was it to use the platform?',
                    'field_type' => 'numericrate',
                    'order' => 3,
                    'rules' => ['required'],
                    'min_val' => '0',
                    'max_val' => '10',
                ],
                [
                    'label' => 'How would you rate communication during the hiring process?',
                    'field_type' => 'numericrate',
                    'order' => 4,
                    'rules' => ['required'],
                    'min_val' => '0',
                    'max_val' => '10',
                ],
                [
                    'label' => 'Suggestions for improvement',
                    'field_type' => 'textarea',
                    'order' => 5,
                ],
                [
                    'label' => 'Additional comments',
                    'field_type' => 'textarea',
                    'order' => 6,
                ],
            ],
            'submit_text' => 'Submit',
            'success_text' => 'Thank you! Your feedback was received!',
            'use_landing_theme' => true,
        ],
        // Candidate additional details form (screening questions, homework etc)
        self::KEY_FORM_TEMPLATE_ADDITIONAL_INFO => [
            'title' => 'Additional info form',
            'type' => Form::TYPE_ADDITIONAL_INFO,
            'formFields' => [
                [
                    'label' => 'Thank you for applying to our position! We would like to know more about you. Please fill in the following fields.',
                    'field_type' => 'static',
                    'order' => 1,
                ],
                [
                    'label' => 'Do you have the right to work in the country where the job is located?',
                    'field_type' => 'select',
                    'order' => 2,
                    'items' => [
                        ['value' => 'yes', 'label' => 'Yes'],
                        ['value' => 'no', 'label' => 'No'],
                    ],
                    'rules' => ['required'],
                ],
                [
                    'label' => 'What is your expected salary?',
                    'field_type' => 'text',
                    'order' => 3,
                    'rules' => ['required'],
                ],
                [
                    'label' => 'Please provide a link to your portfolio or other relevant work',
                    'field_type' => 'text',
                    'order' => 4,
                ],
                [
                    'label' => 'You can leave additional comments here',
                    'field_type' => 'textarea',
                    'order' => 5,
                ],
            ],
            'submit_text' => 'Submit',
            'success_text' => 'Thank you! Your feedback was received!',
            'use_landing_theme' => false,
        ],
        // Candidate cNPS feedback form
        self::KEY_FORM_TEMPLATE_SURVEY => [
            'title' => 'Candidate survey form',
            'type' => Form::TYPE_SURVEY,
            'formFields' => [
                [
                    'label' => 'Thank you for participating in our recruitment process! Your feedback on the collaboration and hiring process would be highly appreciated by the recruitment team.',
                    'field_type' => 'static',
                    'order' => 1,
                ],
                [
                    'label' => 'How likely are you to recommend a friend or colleague to apply for a job with us?',
                    'field_type' => 'cnps',
                    'db_field' => 'cnps',
                    'order' => 2,
                    'rules' => ['required'],
                ],
                [
                    'label' => 'You can leave additional comments here',
                    'field_type' => 'textarea',
                    'db_field' => 'cnps_comment',
                    'order' => 3,
                ],
            ],
            'submit_text' => 'Submit',
            'success_text' => 'Thank you! Your feedback was received!',
            'use_landing_theme' => false,
        ],
        self::KEY_PROJECT_CUSTOM_FIELDS => [],
        self::KEY_CANDIDATE_CUSTOM_FIELDS => [],
        self::KEY_REQUISITION_CUSTOM_FIELDS => [],
        self::KEY_INSTANCE_EMAIL_DOMAIN => null,
        self::KEY_CC_LAST_EMPLOYMENT => true,
        self::KEY_CC_LAST_EDUCATION => false,
        self::KEY_CC_TAGS => true,
        self::KEY_CC_INDICATOR_COMMENTS => true,
        self::KEY_CC_INDICATOR_MESSAGES => true,
        self::KEY_CC_INDICATOR_INVITES => true,
        self::KEY_CC_INDICATOR_VIDEO_INVITES => true,
        self::KEY_CC_INDICATOR_REFERENCES => true,
        self::KEY_CC_EMAIL => false,
        self::KEY_CC_PHONE => false,
        self::KEY_CC_OTHER_ACTIVE_CANDIDACIES => false,
        self::KEY_CC_LOCATION => true,
        self::KEY_CC_TASKS_FROM_ALL_PROJECTS => true,
        self::KEY_CC_SOURCE => false,
        self::KEY_CC_SHOW_INITIALS_IN_BLIND_MODE => false,
        self::KEY_MARK_UNDERAGE_CANDIDATES => false,
        self::KEY_UNDERAGE_AGE_UNDER => 18,
        self::KEY_ALLOW_WITHOUT_EMAIL => false,
        self::KEY_ALLOW_WITHOUT_LOCATION => true,
        self::KEY_USE_PROJECT_LOCATIONS_FEATURE => true,
        self::KEY_ORGANIZATION_LOGO => null,
        self::KEY_ORGANIZATION_FAVICON => null,
        self::KEY_ORGANIZATION_WEBSITE_URL => null,
        self::KEY_ORGANIZATION_ADDRESS => null,
        self::KEY_ORGANIZATION_NAME => null,
        self::KEY_ORGANIZATION_CITY => null,
        self::KEY_ORGANIZATION_STREET_ADDRESS => null,
        self::KEY_ORGANIZATION_REGISTRY_CODE => null,
        self::KEY_INSTANCE_EMAIL_PROVIDER => 'postmark',
        self::KEY_ENABLE_EMAIL_CLICK_TRACKING => true,
        self::KEY_INSTANCE_SMTP_SETTINGS => [],
        self::KEY_BRAND_ABOUT => null,
        self::KEY_BRAND_VIDEO_URL => null,
        self::KEY_COMMENTS_PUBLIC_BY_DEFAULT => false,
        self::KEY_PROJECTS_PRIVATE_BY_DEFAULT => false,
        self::KEY_SHOW_ALL_PROJECT_LOGS_TO_LIMITED => false,
        self::KEY_ENABLE_SCHEDULING_FOR_LIMITED_USERS => false,
        self::KEY_PROJECT_LOGS_PUBLIC_BY_DEFAULT => false,
        self::KEY_MANDATORY_2FA => false,
        self::KEY_LANDING_TEMPLATE => 'default',
        self::KEY_PRIVACY_POLICY_URL => null,
        self::KEY_CONSENT_REQUEST_VALID_DAYS => 7,
        self::KEY_CONSENT_VALID_MONTHS => 36,
        self::KEY_CONSENT_DISPUTE_MONTHS => 12,
        self::KEY_CONSENT_AUTOMATION_ENABLED => false,
        self::KEY_CONSENT_AUTOMATION_SEND_RENEWALS => true,
        self::KEY_CONSENT_NEXT_RENEWALS_DATE => null,
        self::KEY_CONSENT_NEXT_ANONYMIZATION_DATE => null,
        self::KEY_CONSENT_ADMIN_USER_ID => null,
        // The following value is translated in Helpers::getTranslatedSettingDefault
        self::KEY_CONSENT_RENEWAL_SUBJECT => 'Can we contact you about vacancies at [organization_name]?',
        // The following value is translated in Helpers::getTranslatedSettingDefault
        self::KEY_CONSENT_RENEWAL_BODY => <<<'HTML'
<div>Hello, [recipient_full_name]!<div>
<div>We are informing you that [organization_name] is currently storing your personal data. The data might include your
CV, contact information and other information you provided when applying to a position.</div>
<div>If you are interested in our future open positions offers, please give your consent for data processing here: [consent_renewal_url].</div>
<div>Otherwise, we will not contact you about future vacancies at [organization_name].</div>
<div>You can also refuse consent from the link. If you consent to data processing, you can withdraw your consent at any time in the future.
If you ignore this message, we will assume you do not consent to further data processing.</div>
<div>With best</div>
HTML,
        self::KEY_CONSENT_GENERAL_RENEWAL_TEXTS => [
            'en' => 'I am interested in all job offers',
            'et' => 'Olen huvitatud kõikidest tööpakkumistest',
            'lv' => 'Mani interesē visi darba piedāvājumi',
            'lt' => 'Mane domina visi darbo pasiūlymai',
        ],
        self::KEY_HIDE_TAGS_FROM_LIMITED_USERS => false,
        self::KEY_HIDE_FILES_FROM_LIMITED_USERS => false,
        self::KEY_INHERIT_CANDIDATE_FILE_ACCESS_FROM_PROJECT => true,
        self::KEY_ALWAYS_INHERIT_CANDIDATE_ACCESS_FROM_PROJECT => false,
        self::KEY_ALLOW_REGULAR_USERS_CREATE_TAGS => true,
        // The following value is translated in Helpers::getTranslatedSettingDefault
        self::KEY_REQUISITIONS_INFO_MESSAGE => <<<'HTML'
<div>Department: </div>
<div>Compensation (salary + bonus): </div>
<div>Location: </div>
<div>Manager: </div>
<div>Mentor: </div>
<div>Reason for hiring: </div>
HTML,
        self::KEY_HIDE_MESSAGES_FROM_LIMITED_USERS => true,
        self::KEY_SHOW_ALL_PROJECTS_TO_LIMITED_USERS => false,
        // The following value is translated in Helpers::getTranslatedSettingDefault
        self::KEY_SURVEY_QUESTION => 'How likely are you to recommend a friend or colleague to apply for a job with us?',
        self::KEY_PHONE_DEFAULT_COUNTRY_CODE => 'EE',
        self::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS => [],
        self::KEY_REQUISITION_MIN_USERS_APPROVAL => 0,
        self::KEY_ENABLE_PERMANENT_DELETE => false,
        self::KEY_REQUISITION_ORG_CHART => [],
        self::KEY_SIGNATURE_ACCENT_COLOR => '#0d99dd',
        self::KEY_SIGNATURE_STATIC_TEXT => null,
        self::KEY_MS_SSO_TOKEN => null,
        self::KEY_SSO_ALLOW_PASSWORD_LOGINS => false,
        self::KEY_SCIM_SYNC_GROUPS => false,
        self::KEY_LANDING_DOMAIN => null,
        self::KEY_LANDING_HOSTNAME_ID => null,
        self::KEY_LANDING_HIDE_ARCHIVED_PERMANENTLY => false,
        self::KEY_DEFAULT_CAREERS_PAGE_LANDING_ID => null,
        self::KEY_SESSION_LIFETIME_MINUTES => null,
        self::KEY_OUTGOING_EMAIL_ADDRESS_OVERRIDE_MATCH_REGEX => null,
        self::KEY_OUTGOING_EMAIL_ADDRESS_OVERRIDE => null,
        self::KEY_ICAL_WRAP_LINES => true,
        self::KEY_ASK_FOR_DROPOUT_REASON => true,
        self::KEY_USE_CUSTOM_STAGE_CATEGORIES => false,
        self::KEY_MAKE_IMPORT_COMMENTS_PUBLIC => false,
        self::KEY_LOGO_URL => null,
        self::KEY_SEND_TENTATIVE_SCHEDULER_SLOTS => false,
        self::KEY_INTERNAL_NETWORK_ADDR_INFO => null,
        self::KEY_ENABLE_SEND_MAIL_AS_USER => self::SEND_AS_USER_DISABLED,
        self::KEY_ENABLE_LANDING_PREVIEW_WATERMARKS => true,
        self::KEY_AUTOMATICALLY_ARCHIVE_LANDING_PAGES => false,
        self::KEY_BLOCKED_CANDIDATE_EMAILS => [],
        self::KEY_REQUIRE_PROJECT_FAILURE_REASONS => false,
        self::KEY_AI_PROVIDER => self::AI_PROVIDER_OPENAI,
    ];

    const KEY_SESSION_LIFETIME_MINUTES = 'session_lifetime_minutes';

    const ORGANIZATION_TYPE_AGENCY = 'agency';
    const ORGANIZATION_TYPE_CORPORATION = 'corporation';

    const SEND_AS_USER_DISABLED = 'disabled';
    const SEND_AS_USER_ONLY_SERVICE_ACCOUNTS = 'only_service';
    const SEND_AS_USER_ANY_ACCOUNT = 'any';

    const CUSTOM_FIELD_KEYS = [Setting::KEY_CANDIDATE_CUSTOM_FIELDS, Setting::KEY_REQUISITION_CUSTOM_FIELDS, Setting::KEY_PROJECT_CUSTOM_FIELDS];

    /**
     * @var array This might seem ill-advised due to tenant isolation concerns in CLI context, but
     *            the Settings::get method is called so often that hitting redis would be significantly slower
     *            than accessing a static variable.
     */
    public static $cache = [];

    const PER_TEAM_OVERRIDABLE_SETTINGS = [
        //        self::KEY_LANGUAGE,
        self::KEY_TIMEZONE,
        self::KEY_ORGANIZATION_LOGO,
        self::KEY_ORGANIZATION_FAVICON,
        self::KEY_SIGNATURE_ACCENT_COLOR,
        self::KEY_SIGNATURE_STATIC_TEXT,
        self::KEY_ORGANIZATION_WEBSITE_URL,
        self::KEY_ORGANIZATION_ADDRESS,
        self::KEY_ORGANIZATION_NAME,
        self::KEY_ORGANIZATION_REGISTRY_CODE,
        self::KEY_ORGANIZATION_CITY,
        self::KEY_ORGANIZATION_STREET_ADDRESS,
        self::KEY_BRAND_ABOUT,
        self::KEY_BRAND_VIDEO_URL,
        self::KEY_SURVEY_QUESTION,
        self::KEY_PHONE_DEFAULT_COUNTRY_CODE,
        self::KEY_LANDING_HOSTNAME_ID,
        self::KEY_CONSENT_DISPUTE_MONTHS,
        self::KEY_DEFAULT_CAREERS_PAGE_LANDING_ID,
    ];

    /**
     * When settings are fetched from model context or somewhere
     * else where passing team contextually is unrealistic, we can use
     * this static variable to force the team context.
     */
    public static ?Team $teamSettingsContext = null;

    /**
     * About $team
     * Team - get setting for the team, resolved through graph
     * null - resolves team automatically from the set context
     * false - does not use team from context (useful when there are multiple possible contexts)
     *         see User::getSignature
     */
    public static function get(string $key, Team|null|false $team = null)
    {
        start_measure('get_setting');
        if ($team === null && self::$teamSettingsContext) {
            $team = self::$teamSettingsContext;
        }
        $subCacheKey = $team ? $team->id : $team;

        if (!isset(self::$cache[$subCacheKey])) {
            self::$cache[$subCacheKey] = self::getAll($team);
        }

        if (isset(self::$cache[$subCacheKey]) && array_key_exists($key, self::$cache[$subCacheKey])) {
            stop_measure('get_setting');

            return data_get(self::$cache, "$subCacheKey.$key");
        }

        if (!$team && ($resModel = Setting::query()->where('key', $key)->whereNull('team_id')->first())) {
            stop_measure('get_setting');

            return $resModel->value;
        }

        if ($team) {
            /** @var TeamGraphResolver $tgr */
            $tgr = app(TeamGraphResolver::class);
            $settingsForTeam = $tgr->getSettingsFor($team, [$key]);
            $resModel = Arr::first($settingsForTeam);
            if ($resModel) {
                stop_measure('get_setting');

                return $resModel->value;
            }
        }

        stop_measure('get_setting');

        return self::getDefaultValue($key);
    }

    /**
     * This handles settings that store an array of language => value pairs.
     * If new product languages are added, then existing settings stored in the database will not have the new languages,
     * so the result needs to be merged with defaults.
     */
    public static function getMultiLanguageSetting(string $key, Team|null|false $team = null)
    {
        $result = self::get($key, $team);
        if (!$result) {
            return [];
        }

        $defaultLangData = self::getDefaultValue($key);

        return array_replace_recursive($defaultLangData, $result);
    }

    public static function setTeamSettingsContext(?Team $teamSettingsContext): void
    {
        self::$teamSettingsContext = $teamSettingsContext;
    }

    public static function withTeamSettingsContext(callable $callback, ?Team $team = null)
    {
        $oldContext = self::$teamSettingsContext;
        self::setTeamSettingsContext($team);
        $result = $callback();
        self::setTeamSettingsContext($oldContext);

        return $result;
    }

    public function setValueAttribute($v)
    {
        $this->value_json = json_encode($v);
    }

    public function getValueAttribute()
    {
        return json_decode($this->value_json, true);
    }

    public static function set(string $key, $value, ?Team $team = null, bool $clearCache = true): void
    {
        $old = Setting::get($key);
        self::$cache = [];
        /** @var Setting $setting */
        $setting = Setting::firstOrNew(['key' => $key, 'team_id' => $team?->id]);
        $setting->value = self::doCast($key, $value);
        $setting->save();

        if ($clearCache) {
            self::clearSettingsCache();
        }

        self::onUpdated($key, $value, $old);
    }

    public static function doCast($key, $value)
    {
        if (array_key_exists($key, self::$valueCasts) && $value !== null) {
            settype($value, self::$valueCasts[$key]);
        } elseif ($key === Setting::KEY_CANDIDATE_CUSTOM_FIELDS) {
            if (is_array($value)) {
                foreach ($value as &$item) {
                    if ($item['show_on_candidate_card'] === 'true') {
                        $item['show_on_candidate_card'] = true;
                    } else {
                        $item['show_on_candidate_card'] = false;
                    }
                }
            }
        }

        return $value;
    }

    public static function onUpdated(string $key, $value, $old)
    {
        $updateFns = [
            Setting::KEY_CONSENT_AUTOMATION_ENABLED => function ($value, $old) {
                if ($value == $old) {
                    return;
                }
                $service = new ConsentService;
                if ($value) {
                    $service->setNextAnonymizationDate();
                    $service->setNextRenewalsDate();
                }
            },
            Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS => function ($value, $old) {
                if ($value == $old) {
                    return;
                }
                $service = new ConsentService;
                if ($value) {
                    $service->setNextRenewalsDate();
                }
            },
        ];

        if (array_key_exists($key, $updateFns)) {
            return $updateFns[$key]($value, $old);
        }
    }

    public static function getDefaultValue(string $key)
    {
        $translatedValue = Helpers::getTranslatedSettingDefault($key);

        return $translatedValue ?? self::SETTING_DEFAULTS[$key];
    }

    // If you're updating settings in a way where a stale cache could cause issues, increment the number.
    public static $settingsCacheKeyPrefix = 'settings5_';

    public static function getAll(Team|null|false $team = null): array
    {
        start_measure('get_all_settings', 'All settings: ' . ($team?->name ?? 'global'));

        $fullCacheKey = Helpers::prependTenantName(self::$settingsCacheKeyPrefix . ($team ? $team->id : 'global'));

        if (cache()->has($fullCacheKey)) {
            stop_measure('get_all_settings');

            return cache()->get($fullCacheKey);
        }

        if ($team) {
            $allModels = app(TeamGraphResolver::class)->getSettingsFor($team);
        } else {
            $allModels = Setting::query()
                ->select(['id', 'key', 'value_json'])
                ->whereNull('team_id')
                ->get();
        }

        $res = [];
        foreach (self::SETTING_DEFAULTS as $key => $default) {
            $model = $allModels->where('key', $key)->first();
            if ($model) {
                $res[$key] = $model->value;
            } else {
                $res[$key] = self::getDefaultValue($key);
            }
        }

        info("Putting settings to cache: $fullCacheKey");
        cache()->put($fullCacheKey, $res, 60 * 60 * 24);

        stop_measure('get_all_settings');

        return $res;
    }

    public static function isAgency(): bool
    {
        return Setting::get(Setting::KEY_ORGANIZATION_TYPE) === Setting::ORGANIZATION_TYPE_AGENCY;
    }

    public static function getProjectCustomFields(): Collection
    {
        return self::getCustomFields(
            Setting::get(Setting::KEY_PROJECT_CUSTOM_FIELDS),
            6
        );
    }

    /** @return Collection<int, array> */
    public static function getCandidateVisibleCustomFields(): Collection
    {
        return self::getCustomFields(
            Setting::get(Setting::KEY_CANDIDATE_CUSTOM_FIELDS),
            12
        )->filter(fn ($f) => $f['visibility'] !== 'hidden');
    }

    public static function getCustomFields(array $defs, int $elCols = 12): Collection
    {
        return collect($defs)
            ->mapWithKeys(function ($field) use ($elCols) {
                $key = static::convertCustomFieldLabelToSlug($field['label']);
                if ($field['type'] === 'select') {
                    $items = collect($field['items'] ?? [])
                        ->map(fn ($i) => ['value' => static::convertCustomFieldLabelToSlug($i), 'label' => $i])
                        ->prepend(['value' => null, 'label' => '-'])
                        ->toArray();
                } elseif ($field['type'] === 'tags') {
                    $items = collect($field['items'] ?? [])
                        ->map(fn ($i) => ['value' => static::convertCustomFieldLabelToSlug($i), 'label' => $i])
                        ->toArray();
                } else {
                    $items = [];
                }

                return [
                    $key => [
                        'key' => $key,
                        'label' => $field['label'],
                        'type' => $field['type'],
                        'rules' => data_get($field, 'rules', []),
                        'items' => $items,
                        'default' => null,
                        'empty_array_on_null' => true,
                        'visibility' => $field['visibility'] ?? null,
                        'columns' => [
                            'element' => $elCols,
                        ],
                    ],
                ];
            });
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public static function convertCustomFieldLabelToSlug(mixed $label): string
    {
        return Str::slug($label, '_');
    }

    public static function clearSettingsCache()
    {
        Team::query()
            ->get()
            ->map(fn ($t) => Helpers::prependTenantName(self::$settingsCacheKeyPrefix . $t->id))
            ->prepend(Helpers::prependTenantName(self::$settingsCacheKeyPrefix . 'global'))
            ->each(function (string $key) {
                cache()->forget($key);
            });

        app(TeamGraphResolver::class)->clearCache();

        info('Cleared settings cache');
    }
}
