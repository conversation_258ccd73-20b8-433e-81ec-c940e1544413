<?php

namespace App\Services\AI;

use App\Models\File;
use App\Models\FileType;
use App\Models\Interfaces\AiLoggableInterface;
use Illuminate\Support\Str;

class FileDataExtractRequest implements AiRequestInterface
{
    /**
     * @throws \Exception
     */
    public function __construct(public File $file)
    {
        // For image files, we don't need text contents since we'll analyze the image directly
        if ($this->file->isImage()) {
            return;
        }

        if (!$this->file->has_contents) {
            $this->file->setContents();
            $this->file->refresh();
        }

        if (!$this->file->contents) {
            throw new \Exception('File contents are empty.');
        }
    }

    public function getSystemPrompt(): string
    {
        $availableCustomFileTypes = FileType::query()
            ->orderBy('id')
            ->get()
            ->mapWithKeys(function ($fileType) {
                return [$fileType->id => $fileType->name];
            })->jsonSerialize();

        $availableCustomFileTypes = json_encode($availableCustomFileTypes, JSON_PRETTY_PRINT);

        return <<<PROMPT
                You are a tool integrated within an applicant tracking system (ATS). Your primary function is to extract
                and structure data from a file according to a specific predefined format. Please follow these detailed
                guidelines meticulously:

                Date Extraction:
                * Relevant Dates: Only extract dates that imply the document’s usability or validity, such as issuance dates, validity periods, or expiration dates.
                * Irrelevant Dates: Do not extract dates that are about past events unrelated to the document’s characteristics, such as employment histories on a CV.
                * Validity Period: If the document specifies both a start and end date indicating its validity period, extract both. If only one date is specified, determine whether it is a start or end date or related to document issuance.
                * Missing Dates: If the document lacks pertinently valid dates, set both start and end dates to null.

                Data Transcription & Formatting:
                * Transcription Accuracy: Transcribe all data as it appears without modifications or corrections.
                * Date Formatting: Format all extracted dates using the ISO8601 standard (YYYY-MM-DD).
                * Enum Values: For fields involving enumerations, ensure the value matches specified enumerator options or set to null if unspecified.

                Error Handling:
                * Ensure no assumptions are made beyond what the document states. Address any ambiguous dates with clear reasoning or default to null if uncertain.

                Contextual File Types:
                These are the available file types with their corresponding IDs:
                $availableCustomFileTypes

                Note: The user will send you either the text contents of a file or an image of the document. For images, analyze the visual content to extract the same information as you would from text.

                By adhering to these guidelines, provide structured and precisely formatted data to align with the ATS system requirements.
                PROMPT;
    }

    public function getUserPrompt(): string
    {
        if ($this->file->isImage()) {
            return 'Extract data from the document shown in the image. Focus on identifying dates, document types, and any validity periods or expiration information.';
        }

        return Str::limit("Extract data from the following file.\n\n" . $this->file->contents, 7000, '...');
    }

    public function getAiLoggable(): AiLoggableInterface
    {
        return $this->file;
    }

    public function updateDataAndGetFields(array $responseData): array
    {
        $keys = ['start_date', 'end_date', 'type', 'file_type_id'];

        if ($this->file->file_type_id) {
            $keys = array_diff($keys, ['file_type_id', 'type']);
        }

        foreach ($keys as $key) {
            $this->file->$key = $responseData[$key];
        }

        $this->file->saveQuietly();

        return $keys;
    }

    public function getResponseFormat(): array
    {
        return [
            'type' => 'json_schema',
            'json_schema' => [
                'name' => 'date_extraction',
                'schema' => [
                    'type' => 'object',
                    'properties' => [
                        'start_date' => [
                            'type' => ['string', 'null'],
                            'description' => 'The start date of the validity of the document.',
                        ],
                        'end_date' => [
                            'type' => ['string', 'null'],
                            'description' => 'The end date of the validity of the document.',
                        ],
                        'type' => [
                            'type' => ['string', 'null'],
                            'description' => 'The type of the document.',
                            'enum' => [File::TYPE_CV, File::TYPE_CANDIDATE_OTHER],
                        ],
                        'file_type_id' => [
                            'type' => ['number', 'null'],
                            'description' => 'The ID of the document type, if the file is of type candidate_other.',
                        ],
                    ],
                    'required' => ['start_date', 'end_date', 'type', 'file_type_id'],
                    'additionalProperties' => false,
                ],
                'strict' => true,
            ],
        ];
    }

    public function filePath(): ?string
    {
        if ($this->file->isImage()) {
            try {
                return $this->file->getLocalPath();
            } catch (\Exception $e) {
                // If we can't get the local path, fall back to text-only processing
                return null;
            }
        }

        return null;
    }
}
