<?php

namespace App\Services\AI;

use App\Models\Interfaces\AiLoggableInterface;

interface AiRequestInterface
{
    public function getSystemPrompt(): string;

    public function getUserPrompt(): string;

    public function getResponseFormat(): array;

    public function getAiLoggable(): AiLoggableInterface;

    public function updateDataAndGetFields(array $responseData): array;

    /**
     * Get the file path for an image to be included in the AI request.
     * Used for vision-enabled AI models to analyze images.
     *
     * @return string|null The absolute path to the image file, or null if no image is needed
     */
    public function filePath(): ?string;
}
